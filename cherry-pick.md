# How to Cherry-Pick Commits in Git

## Overview

Cherry-picking in Git allows you to select specific commits from one branch and apply them to another. This guide will walk you through the process of cherry-picking commits with a real-world example from our project.

## When to Use Cherry-Pick

- Applying a bugfix to multiple branches
- Backporting features to older release branches
- Selecting specific changes when you don't want to merge an entire branch

## Real-World Example

Let's say we have the following commit history in our Bitbucket repository:

Check Reference Image below

We want to cherry-pick the three latest commits starting from the `prerelease-3.6.3` tag.

### Steps:

1. Checkout the prerelease tag:
   ```bash
   git checkout prerelease-3.6.3
   ```

2. Cherry-pick the desired range of commits:
   ```bash
   git cherry-pick e3f452a^..bbbcc6c
   ```

> **Note:** The `^` after `e3f452a` means it will include the commit before it. Without the `^`, it would only get the two latest commits.

This command will apply the following commits to your current branch:

- bbbcc6c: SBE-4207 Fans United list item validation fixes
- 698a56b: SBE-4156 Implement automatic article origins
- e3f452a: SBE-4197 now receiving exclude_category_ids on the /latest endpoint

### Notes on commits to select for cherry-picking

In a perfect scenario, the developer would not need to cherry-pick but would be able to tag master. However, since this is often not the case, one approach would be using the `git cherry -v <tag_name>` command. For example:

1. `git checkout master`

2. `git cherry -v release-4.8.2`

3. This would yield output such as the following:

   ```
   - 100681123b0a01aedc06d969d0585fada1835276 Merged in SBE-4186 (pull request #833)
   + caaeb8a249ab0334a575f271fd2f606e32bd2aa4 Merged in SBE-4126 (pull request #832)
   + fca0cc1328877bcf484c58ef7d1acfa3154c65c4 Merged in SBE-3962 (pull request #829)
   - fbb4426f592d10c22a055a855a5a2586a40d5642 Merged in SBE-4186-2 (pull request #834)
   - c11b3d3ec0a22d5003c4696b0a449b62341856ff Merged in SBE-4204 (pull request #835)
   + 7b802e2a06496908546bbe343124726be784a38c Test deploy
   + b31e413b54464d66091b64b7886dd8d98e8b9137 Force build
   + e5a5481d06b0b7d08af6fdd74ee5ef7a328a2293 Force build
   - b42d0fe20e14aeccbcb978739e57db285215d2fb Merged in SBE-4208 (pull request #837)
   + 878b962d9cef2deadd0135bf5b49c9fe2698c06d Force build
   + d2694cc812866551714085195d3342decadef3a5 Merged in SBE-3962 (pull request #836)
   + ecb96065daa9a92c2e23efa9b69b14e8668ab3fb Merged in SBE-3962 (pull request #839)
   - 70dbe59bfb24e718654e5343dd00eb0e54dad4d9 Merged in SBE-4210 (pull request #838)
   + fa4c1a600da4c303f18eaf1a32a9100f20ec7f5b Merged in SBE-4119 (pull request #840)
   + 6ac428ea8a75cb1fe4c84fc830df8ec90f7751d2 Merged in SBE-4126 (pull request #842)
   + 9499806e8d2d8d36d45fef2b1cf88b2b456fed12 Merged in SBE-3962 (pull request #841)
   ```

   In this output:
   - every commit preceded by a minus sign is present in both the `master` branch and the `release-4.8.2` tag, hence there's no need to act on it.
   - every commit preceded by a plus sign is present in the `master` branch but not in the `release-4.8.2` tag, hence the developer needs to examine it in order to decide whether or not it should be included.

## General Steps to Cherry-Pick a Commit

1. **Identify the Commit to Cherry-Pick**
   - Use `git log` or check bitbucket to find the commit hash of the change you want to apply
   - Copy the full commit hash (or at least the first 7 characters)

2. **Switch to the Target Branch**
   - Checkout the branch where you want to apply the commit
   - Ensure your working directory is clean (no uncommitted changes)

3. **Perform the Cherry-Pick**
   - Use the `git cherry-pick` command followed by the commit hash or range

4. **Resolve Conflicts (if any)**
   - If Git encounters conflicts, resolve them manually
   - After resolving, stage the changes and continue the cherry-pick

5. **Complete the Cherry-Pick**
   - If there were no conflicts, the cherry-pick completes automatically
   - If you resolved conflicts, finish with `git cherry-pick --continue`

6. **Push Changes**
   - Push the changes to the remote repository

## Best Practices

- Always cherry-pick onto a clean working branch
- Use cherry-pick sparingly; prefer merging when appropriate
- Communicate with your team when cherry-picking across shared branches
- When cherry-picking multiple commits, use the range syntax (e.g., `e3f452a^..bbbcc6c`) to ensure you get all desired commits
- Double-check the commit hashes before cherry-picking to avoid mistakes

## Troubleshooting

- If you encounter issues during cherry-pick, you can abort the operation with `git cherry-pick --abort`
- For complex cherry-picks, consider using interactive rebase instead
- If you accidentally cherry-pick the wrong commits, you can undo the operation with `git reset --hard HEAD~n` where n is the number of commits you want to undo
