# Article Scheduler Service - Functional Domain Architecture Refactoring Plan

## Overview
This document outlines the detailed refactoring plan to reorganize the Article Scheduler Service codebase into a **functional domain-based architecture**. Each functional domain will have its own 4-tier architecture (application, domain, infrastructure, presentation), creating better separation of concerns and improved maintainability.

## Target Architecture Structure

The new structure will organize code by **functional domains** rather than by architectural layers:

```
com.sportal365.articlescheduler/
├── schedules/                    # Schedule Management Domain
│   ├── application/
│   ├── domain/
│   ├── infrastructure/
│   └── presentation/
├── article/
│   └── generation/               # Article Generation Domain
│       ├── application/
│       ├── domain/
│       ├── infrastructure/
│       └── presentation/
├── sportsdata/                   # Keep unchanged
└── debug/                        # Keep unchanged
```

## Files to Keep Unchanged

1. `src/main/java/com/sportal365/articlescheduler/sportsdata/` - **Keep as-is** (entire folder)
2. `src/main/java/com/sportal365/articlescheduler/debug/` - **Keep as-is** (entire folder)

## Functional Domain Analysis

### Domain 1: Schedules Management (`com.sportal365.articlescheduler.schedules`)

This domain handles all schedule-related functionality including creation, management, processing, and lifecycle operations.

#### Files to Move to Schedules Domain:

**Application Layer** (`schedules/application/`):
```
# Services
application/service/schedules/ScheduleService.java
→ schedules/application/service/ScheduleService.java

application/service/schedules/SchedulePersistenceService.java
→ schedules/application/service/SchedulePersistenceService.java

application/service/schedules/processing/ScheduleProcessingService.java
→ schedules/application/service/processing/ScheduleProcessingService.java

# DTOs
application/dto/schedule/request/ScheduleCreateRequest.java
→ schedules/application/dto/request/ScheduleCreateRequest.java

application/dto/schedule/request/ScheduleListRequest.java
→ schedules/application/dto/request/ScheduleListRequest.java

application/dto/schedule/request/ScheduleMatchRequest.java
→ schedules/application/dto/request/ScheduleMatchRequest.java

application/dto/schedule/request/ScheduleUpdateRequest.java
→ schedules/application/dto/request/ScheduleUpdateRequest.java

application/dto/schedule/response/ScheduleResponse.java
→ schedules/application/dto/response/ScheduleResponse.java

application/dto/schedule/response/ScheduleCountResponse.java
→ schedules/application/dto/response/ScheduleCountResponse.java
```

**Domain Layer** (`schedules/domain/`):
```
# Models
domain/model/Schedule.java
→ schedules/domain/model/Schedule.java

domain/model/enums/ScheduleStatus.java
→ schedules/domain/model/enums/ScheduleStatus.java

# Mappers
domain/model/mappers/ScheduleMapper.java
→ schedules/domain/model/mappers/ScheduleMapper.java

# Repositories
domain/repository/ScheduleRepository.java
→ schedules/domain/repository/ScheduleRepository.java

# Query Builders
domain/query/ScheduleQueryBuilder.java
→ schedules/domain/query/ScheduleQueryBuilder.java

# Validators
domain/validator/ScheduleRequestValidator.java
→ schedules/domain/validator/ScheduleRequestValidator.java
```

**Infrastructure Layer** (`schedules/infrastructure/`):
```
# Persistence (if any schedule-specific persistence logic exists)
# Configuration (if any schedule-specific configs exist)
```

**Presentation Layer** (`schedules/presentation/`):
```
# Controllers
presentation/controller/ScheduleController.java
→ schedules/presentation/controller/ScheduleController.java
```

### Domain 2: Article Generation (`com.sportal365.articlescheduler.article.generation`)

This domain handles all article generation functionality including LLM integration, content creation, template processing, and publishing.

#### Files to Move to Article Generation Domain:

**Application Layer** (`article/generation/application/`):
```
# Services
application/service/article/generation/ArticleGenerationService.java
→ article/generation/application/service/ArticleGenerationService.java

application/service/article/generation/AsyncArticleGenerationService.java
→ article/generation/application/service/AsyncArticleGenerationService.java

application/service/article/generation/LlmService.java
→ article/generation/application/service/LlmService.java

application/service/article/content/ContentService.java
→ article/generation/application/service/ContentService.java

application/service/article/content/EditorBlockService.java
→ article/generation/application/service/EditorBlockService.java

application/service/article/templates/TemplateGenerationService.java
→ article/generation/application/service/TemplateGenerationService.java

# DTOs
application/dto/article/ArticleDTO.java
→ article/generation/application/dto/ArticleDTO.java

application/dto/article/ArticleGenerationRequestDto.java
→ article/generation/application/dto/ArticleGenerationRequestDto.java

application/dto/article/response/ArticleResponseDto.java
→ article/generation/application/dto/response/ArticleResponseDto.java
```

**Domain Layer** (`article/generation/domain/`):
```
# Models
domain/model/ArticleExample.java
→ article/generation/domain/model/ArticleExample.java

domain/model/Template.java
→ article/generation/domain/model/Template.java

domain/model/ParagraphTemplate.java
→ article/generation/domain/model/ParagraphTemplate.java

# LLM Models
domain/llm/model/dto/ArticleStructure.java
→ article/generation/domain/model/llm/ArticleStructure.java

domain/llm/model/dto/ContentRequestDTO.java
→ article/generation/domain/model/llm/ContentRequestDTO.java

domain/llm/model/dto/ContentResponseDTO.java
→ article/generation/domain/model/llm/ContentResponseDTO.java

domain/llm/enums/ContentStatus.java
→ article/generation/domain/model/enums/ContentStatus.java

# Template Models
domain/template/model/PromptTemplate.java
→ article/generation/domain/model/template/PromptTemplate.java

domain/template/model/TemplateSection.java
→ article/generation/domain/model/template/TemplateSection.java
```

**Infrastructure Layer** (`article/generation/infrastructure/`):
```
# Clients
infrastructure/client/llm/LlmClient.java
→ article/generation/infrastructure/client/LlmClient.java

infrastructure/client/contentapi/ContentApiClient.java
→ article/generation/infrastructure/client/ContentApiClient.java

# Persistence
infrastructure/persistence/repository/TemplateRepository.java
→ article/generation/infrastructure/persistence/TemplateRepository.java

infrastructure/persistence/repository/ParagraphTemplateRepository.java
→ article/generation/infrastructure/persistence/ParagraphTemplateRepository.java

infrastructure/persistence/entity/TemplateDocument.java
→ article/generation/infrastructure/persistence/entity/TemplateDocument.java
```

**Presentation Layer** (`article/generation/presentation/`):
```
# Controllers
presentation/controller/TemplateController.java
→ article/generation/presentation/controller/TemplateController.java

presentation/controller/ParagraphTemplateController.java
→ article/generation/presentation/controller/ParagraphTemplateController.java
```

## Shared Components Analysis

### Components That Remain in Root Package

These components are shared across multiple domains and should remain in the root package structure:

**Shared Application Components**:
```
# Keep in root application/
application/service/ProjectService.java          # Used by both domains
application/dto/common/                          # Shared DTOs
application/util/WidgetInputValidator.java       # Shared utility

# Move debug to common
application/service/debug/SaveTemplateToMongoService.java
→ application/service/common/SaveTemplateToMongoService.java
```

**Shared Domain Components**:
```
# Keep in root domain/
domain/model/MatchDetails.java                   # Used by both domains
domain/model/Widget.java                         # Used by article generation
domain/model/enums/                              # Shared enums (except ScheduleStatus)
domain/calculator/ArticleCalculator.java        # Shared business logic
domain/exception/                                # Shared exceptions
domain/utils/DateUtils.java                     # Shared utilities
domain/utils/ProjectUtils.java                  # Shared utilities
domain/widget/                                   # Widget functionality (shared)
```

**Shared Infrastructure Components**:
```
# Keep in root infrastructure/
infrastructure/config/                          # Shared configurations
infrastructure/constant/Constants.java          # Shared constants
infrastructure/events/                          # Shared events
infrastructure/persistence/migration/           # Database migrations
infrastructure/client/common/                   # Shared client utilities
```

**Shared Presentation Components**:
```
# Keep in root presentation/
presentation/advice/CustomControllerAdvice.java # Global exception handling
presentation/constant/Constants.java            # Presentation constants
presentation/filter/ProjectInterceptor.java     # Global filters
presentation/controller/MigrationController.java # Shared functionality
presentation/controller/WidgetController.java   # Widget management
```

## Step-by-Step Refactoring Process

### Step 1: Create New Directory Structure
```bash
# Create schedules domain structure
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/application/service
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/application/dto/request
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/application/dto/response
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/domain/model/enums
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/domain/model/mappers
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/domain/repository
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/domain/query
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/domain/validator
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/infrastructure
mkdir -p src/main/java/com/sportal365/articlescheduler/schedules/presentation/controller

# Create article generation domain structure
mkdir -p src/main/java/com/sportal365/articlescheduler/article/generation/application/service
mkdir -p src/main/java/com/sportal365/articlescheduler/article/generation/application/dto/response
mkdir -p src/main/java/com/sportal365/articlescheduler/article/generation/domain/model/llm
mkdir -p src/main/java/com/sportal365/articlescheduler/article/generation/domain/model/template
mkdir -p src/main/java/com/sportal365/articlescheduler/article/generation/domain/model/enums
mkdir -p src/main/java/com/sportal365/articlescheduler/article/generation/infrastructure/client
mkdir -p src/main/java/com/sportal365/articlescheduler/article/generation/infrastructure/persistence/repository
mkdir -p src/main/java/com/sportal365/articlescheduler/article/generation/infrastructure/persistence/entity
mkdir -p src/main/java/com/sportal365/articlescheduler/article/generation/presentation/controller

# Create common service directory
mkdir -p src/main/java/com/sportal365/articlescheduler/application/service/common
```

### Step 2: Move Files by Domain (Phase-by-Phase)

#### Phase 2.1: Move Schedules Domain Files
```bash
# Application Layer - Services
mv application/service/schedules/ScheduleService.java \
   schedules/application/service/ScheduleService.java

mv application/service/schedules/SchedulePersistenceService.java \
   schedules/application/service/SchedulePersistenceService.java

mv application/service/schedules/processing/ScheduleProcessingService.java \
   schedules/application/service/processing/ScheduleProcessingService.java

# Application Layer - DTOs
mv application/dto/schedule/request/* \
   schedules/application/dto/request/

mv application/dto/schedule/response/* \
   schedules/application/dto/response/

# Domain Layer
mv domain/model/Schedule.java \
   schedules/domain/model/Schedule.java

mv domain/model/enums/ScheduleStatus.java \
   schedules/domain/model/enums/ScheduleStatus.java

mv domain/model/mappers/ScheduleMapper.java \
   schedules/domain/model/mappers/ScheduleMapper.java

mv domain/repository/ScheduleRepository.java \
   schedules/domain/repository/ScheduleRepository.java

mv domain/query/ScheduleQueryBuilder.java \
   schedules/domain/query/ScheduleQueryBuilder.java

mv domain/validator/ScheduleRequestValidator.java \
   schedules/domain/validator/ScheduleRequestValidator.java

# Presentation Layer
mv presentation/controller/ScheduleController.java \
   schedules/presentation/controller/ScheduleController.java
```

#### Phase 2.2: Move Article Generation Domain Files
```bash
# Application Layer - Services
mv application/service/article/generation/* \
   article/generation/application/service/

mv application/service/article/content/* \
   article/generation/application/service/

mv application/service/article/templates/* \
   article/generation/application/service/

# Application Layer - DTOs
mv application/dto/article/* \
   article/generation/application/dto/

# Domain Layer - Models
mv domain/model/ArticleExample.java \
   article/generation/domain/model/ArticleExample.java

mv domain/model/Template.java \
   article/generation/domain/model/Template.java

mv domain/model/ParagraphTemplate.java \
   article/generation/domain/model/ParagraphTemplate.java

# Domain Layer - LLM Models
mv domain/llm/model/dto/* \
   article/generation/domain/model/llm/

mv domain/llm/enums/ContentStatus.java \
   article/generation/domain/model/enums/ContentStatus.java

# Domain Layer - Template Models
mv domain/template/model/* \
   article/generation/domain/model/template/

# Infrastructure Layer
mv infrastructure/client/llm/* \
   article/generation/infrastructure/client/

mv infrastructure/client/contentapi/* \
   article/generation/infrastructure/client/

mv infrastructure/persistence/repository/TemplateRepository.java \
   article/generation/infrastructure/persistence/repository/TemplateRepository.java

mv infrastructure/persistence/repository/ParagraphTemplateRepository.java \
   article/generation/infrastructure/persistence/repository/ParagraphTemplateRepository.java

mv infrastructure/persistence/entity/TemplateDocument.java \
   article/generation/infrastructure/persistence/entity/TemplateDocument.java

# Presentation Layer
mv presentation/controller/TemplateController.java \
   article/generation/presentation/controller/TemplateController.java

mv presentation/controller/ParagraphTemplateController.java \
   article/generation/presentation/controller/ParagraphTemplateController.java
```

#### Phase 2.3: Move Common Services
```bash
# Rename debug to common
mv application/service/debug/SaveTemplateToMongoService.java \
   application/service/common/SaveTemplateToMongoService.java
```

## Package Import Updates Required

After moving files, the following import statements will need to be updated:

### 1. Schedules Domain Imports
```java
// Files that import schedule-related components need updates:

// OLD imports:
import com.sportal365.articlescheduler.application.service.schedules.*;
import com.sportal365.articlescheduler.application.dto.schedule.*;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleStatus;
import com.sportal365.articlescheduler.domain.repository.ScheduleRepository;
import com.sportal365.articlescheduler.presentation.controller.ScheduleController;

// NEW imports:
import com.sportal365.articlescheduler.schedules.application.service.*;
import com.sportal365.articlescheduler.schedules.application.dto.request.*;
import com.sportal365.articlescheduler.schedules.application.dto.response.*;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.schedules.domain.model.enums.ScheduleStatus;
import com.sportal365.articlescheduler.schedules.domain.repository.ScheduleRepository;
import com.sportal365.articlescheduler.schedules.presentation.controller.ScheduleController;
```

### 2. Article Generation Domain Imports
```java
// Files that import article generation components need updates:

// OLD imports:
import com.sportal365.articlescheduler.application.service.article.generation.*;
import com.sportal365.articlescheduler.application.service.article.content.*;
import com.sportal365.articlescheduler.application.service.article.templates.*;
import com.sportal365.articlescheduler.application.dto.article.*;
import com.sportal365.articlescheduler.domain.model.ArticleExample;
import com.sportal365.articlescheduler.domain.model.Template;
import com.sportal365.articlescheduler.domain.llm.model.dto.*;
import com.sportal365.articlescheduler.domain.template.model.*;
import com.sportal365.articlescheduler.infrastructure.client.llm.*;
import com.sportal365.articlescheduler.infrastructure.client.contentapi.*;

// NEW imports:
import com.sportal365.articlescheduler.article.generation.application.service.*;
import com.sportal365.articlescheduler.article.generation.application.dto.*;
import com.sportal365.articlescheduler.article.generation.application.dto.response.*;
import com.sportal365.articlescheduler.article.generation.domain.model.ArticleExample;
import com.sportal365.articlescheduler.article.generation.domain.model.Template;
import com.sportal365.articlescheduler.article.generation.domain.model.llm.*;
import com.sportal365.articlescheduler.article.generation.domain.model.template.*;
import com.sportal365.articlescheduler.article.generation.infrastructure.client.*;
```

### 3. Common Service Imports
```java
// OLD imports:
import com.sportal365.articlescheduler.application.service.debug.*;

// NEW imports:
import com.sportal365.articlescheduler.application.service.common.*;
```

## Critical Dependencies Analysis

### High-Impact Cross-Domain Dependencies:

1. **ScheduleProcessingService** → **AsyncArticleGenerationService**
   - Location: `schedules/application/service/processing/ScheduleProcessingService.java`
   - Dependency: `article/generation/application/service/AsyncArticleGenerationService.java`
   - Impact: Schedules domain depends on article generation domain

2. **ArticleGenerationService** → **ScheduleRepository**
   - Location: `article/generation/application/service/ArticleGenerationService.java`
   - Dependency: `schedules/domain/repository/ScheduleRepository.java`
   - Impact: Article generation domain depends on schedules domain

3. **LlmService** → **Schedule Model**
   - Location: `article/generation/application/service/LlmService.java`
   - Dependency: `schedules/domain/model/Schedule.java`
   - Impact: Article generation domain depends on schedules domain

### Shared Dependencies (Remain in Root):
- **MatchDetails**: Used by both domains, stays in root `domain/model/`
- **ProjectService**: Used by both domains, stays in root `application/service/`
- **Widget**: Used by article generation, stays in root `domain/model/`

## Spring Configuration Impact

### Component Scanning Updates:
```java
// Current configuration in ArticleSchedulerApplication.java:
@ComponentScan(basePackages = "com.sportal365.articlescheduler")

// After refactoring, ensure these paths are scanned:
@ComponentScan(basePackages = {
    "com.sportal365.articlescheduler",                    // Root components
    "com.sportal365.articlescheduler.schedules",          // Schedules domain
    "com.sportal365.articlescheduler.article.generation", // Article generation domain
    "com.sportal365.articlescheduler.sportsdata",         // Sportsdata (unchanged)
    "com.sportal365.articlescheduler.debug"               // Debug (unchanged)
})
```

### Bean Registration Verification:
- All moved services must maintain their Spring annotations (@Service, @Component, @Repository)
- All moved controllers must maintain @RestController annotation
- All moved mappers must maintain @Mapper annotation

## Validation Steps

### After Each Phase:
1. **Compile Check**: `./gradlew build`
2. **Test Execution**: `./gradlew test`
3. **Spring Context**: Verify application starts without errors
4. **Import Validation**: Check for missing imports or broken references

### Integration Testing:
1. **Schedule Creation**: Test schedule creation endpoint
2. **Article Generation**: Test article generation process
3. **Cross-Domain Flow**: Test complete schedule → article generation flow
4. **Widget Functionality**: Test widget generation and embedding

## Benefits of Functional Domain Architecture

1. **Domain Isolation**: Each domain is self-contained with clear boundaries
2. **Team Ownership**: Different teams can own different domains
3. **Independent Development**: Domains can evolve independently
4. **Easier Testing**: Domain-specific testing becomes more focused
5. **Scalability**: New domains can be added following the same pattern
6. **Microservice Ready**: Domains can be extracted to separate services if needed

## Potential Challenges and Mitigation

### Challenge 1: Cross-Domain Dependencies
- **Issue**: Schedules and Article Generation domains depend on each other
- **Mitigation**: Use interfaces and dependency injection to minimize coupling

### Challenge 2: Shared Model Objects
- **Issue**: MatchDetails and Schedule are used across domains
- **Mitigation**: Keep shared models in root package, consider creating domain-specific DTOs

### Challenge 3: Complex Import Updates
- **Issue**: Many files will need import statement updates
- **Mitigation**: Use IDE refactoring tools, implement changes incrementally

## Timeline Estimate

- **Phase 1 (Directory Creation)**: 1-2 hours
- **Phase 2.1 (Schedules Domain)**: 6-8 hours
- **Phase 2.2 (Article Generation Domain)**: 8-10 hours
- **Phase 2.3 (Common Services)**: 1-2 hours
- **Phase 3 (Import Updates)**: 8-12 hours
- **Phase 4 (Testing & Validation)**: 6-8 hours

**Total Estimated Time**: 30-42 hours

## Rollback Plan

1. **Version Control**: Create feature branch for refactoring
2. **Incremental Commits**: Commit after each successful phase
3. **Backup Strategy**: Keep original structure until full validation
4. **Rollback Triggers**: Any breaking changes that cannot be resolved within 2 hours

---

*This functional domain architecture refactoring creates clear separation between schedule management and article generation concerns while maintaining shared components in the root package structure.*


