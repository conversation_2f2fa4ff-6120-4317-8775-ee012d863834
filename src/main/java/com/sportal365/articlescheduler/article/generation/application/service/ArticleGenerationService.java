package com.sportal365.articlescheduler.article.generation.application.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sportal365.articlescheduler.article.generation.application.dto.ArticleGenerationRequestDto;
import com.sportal365.articlescheduler.article.generation.application.dto.response.ArticleResponseDto;
import com.sportal365.articlescheduler.article.generation.domain.model.llm.ContentResponseDTO;
import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.shared.domain.model.enums.TemplateTypeEnum;
import com.sportal365.articlescheduler.schedules.domain.repository.ScheduleRepository;
import com.sportal365.articlescheduler.article.generation.domain.model.template.PromptTemplate;
import com.sportal365.articlescheduler.article.generation.infrastructure.client.model.ArticleContent;
import com.sportal365.articlescheduler.sportsdata.match.MatchDetailsService;
import com.sportal365.articlescheduler.article.generation.application.service.TemplateGenerationService;
import com.sportal365.articlescheduler.article.generation.application.service.ContentService;
import com.sportal365.articlescheduler.article.generation.application.service.EditorBlockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;

@Service
@RequiredArgsConstructor
@Slf4j
public class ArticleGenerationService {

    private final ScheduleRepository scheduleMongoRepository;

    private final MatchDetailsService matchDetailsService;

    private final TemplateGenerationService templateService;

    private final ContentService contentService;

    private final EditorBlockService editorBlockService;

    private final LlmService llmService;

    /**
     * Generates an article based on the provided schedule.
     * This is a convenience method that wraps the schedule ID in an ArticleGenerationRequestDto
     * and delegates to the main generate method.
     *
     * @param schedule the schedule containing match information and generation parameters
     * @return ArticleResponseDto containing the generated article details and metadata
     * @throws JsonProcessingException if there's an error processing JSON during article generation
     */
    public ArticleResponseDto generateArticle(Schedule schedule) throws JsonProcessingException {
        return generate(new ArticleGenerationRequestDto(schedule.getId()));
    }

    /**
     * Generates an article based on the provided request.
     * This is the main article generation method that orchestrates the entire process:
     * 1. Retrieves the schedule from the repository
     * 2. Fetches match details
     * 3. Generates a prompt template
     * 4. Uses LLM to generate article content
     * 5. Builds editor blocks for the content
     * 6. Posts the article to the content API
     * 7. Updates the schedule with the article ID
     *
     * @param request the article generation request containing the schedule ID
     * @return ArticleResponseDto containing the generated article details, usage statistics, and metadata
     * @throws JsonProcessingException if there's an error processing JSON during any step of the generation
     * @throws IllegalArgumentException if no schedule is found for the given ID
     */
    public ArticleResponseDto generate(ArticleGenerationRequestDto request) throws JsonProcessingException {
        String id = request.id();

        log.debug("Starting article generation process for Schedule with ID: {}", id);

        Schedule schedule = scheduleMongoRepository.findById(id).orElse(null);

        if (schedule == null) {
            log.warn("No schedule found with ID: {}", id);
            throw new IllegalArgumentException("No schedule found for the given ID");
        }

        MatchDetails matchDetails = matchDetailsService.getMatchDetails(schedule);
        PromptTemplate promptTemplate = templateService.generateTemplate(
                matchDetails, schedule.getTemplateName(), getDisplayLanguage(schedule.getLanguage()), schedule.getProviderProperties(),
                TemplateTypeEnum.PRE_GAME, schedule.getProjectDomain());

        ContentResponseDTO generatedArticle = generateArticle(promptTemplate, matchDetails, schedule);

        List<ArticleContent.ArticleBlock> editorBlocks = editorBlockService.buildEditorBlocks(
                generatedArticle.getGeneratedContent(),
                matchDetails,
                promptTemplate.getIndexedSections(),
                schedule.getProjectDomain(),
                schedule.getLanguage());

        String postedArticleId = contentService.postArticle(generatedArticle.getGeneratedContent(), schedule, matchDetails, editorBlocks);
        schedule.setArticleId(postedArticleId);
        scheduleMongoRepository.save(schedule);
        return buildResponse(postedArticleId, schedule, generatedArticle);
    }

    /**
     * Converts a language code to its display name in English.
     * For example, "es" becomes "Spanish", "fr" becomes "French".
     *
     * @param language the language code (e.g., "en", "es", "fr")
     * @return the display name of the language in English
     */
    private String getDisplayLanguage(String language) {
        Locale locale = new Locale(language);

        return locale.getDisplayLanguage(Locale.ENGLISH);
    }

    /**
     * Generates article content using the LLM service.
     * This method calls the LLM service with the provided template and match details,
     * and blocks until the response is received.
     *
     * @param promptTemplate the template containing prompts and structure for article generation
     * @param matchDetails the details of the match to generate the article about
     * @param schedule the schedule containing additional context and configuration
     * @return ContentResponseDTO containing the generated article content and metadata
     */
    private ContentResponseDTO generateArticle(
            PromptTemplate promptTemplate, MatchDetails matchDetails, Schedule schedule) {
        return llmService.generateArticle(promptTemplate, matchDetails, schedule).block();
    }

    /**
     * Builds the response DTO containing article generation results and metadata.
     * This method extracts usage statistics from the generated article and creates
     * a comprehensive response object with all relevant information.
     *
     * @param postedArticleId the ID of the article that was posted to the content API
     * @param schedule the schedule that was used for article generation
     * @param generatedArticle the response from the LLM containing content and metadata
     * @return ArticleResponseDto containing the article ID, match ID, template type, and usage statistics
     */
    private ArticleResponseDto buildResponse(String postedArticleId, Schedule schedule, ContentResponseDTO generatedArticle) {

        ArticleResponseDto.Usage usageDto = new ArticleResponseDto.Usage(
                generatedArticle.getPromptMetadata().getUsage().getGenerationTokens(),
                generatedArticle.getPromptMetadata().getUsage().getPromptTokens()
        );
        log.info("Generated article ID: {} with schedule_id: {}", postedArticleId, schedule.getId() );

        return new ArticleResponseDto(TemplateTypeEnum.PRE_GAME.name(), schedule.getMatchDetails().getMatchId(), postedArticleId, usageDto);
    }

}
