package com.sportal365.articlescheduler.article.generation.application.service;

import com.sportal365.articlescheduler.article.generation.application.dto.ArticleDTO;
import com.sportal365.articlescheduler.article.generation.domain.model.llm.ArticleStructure;
import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.article.generation.infrastructure.client.ContentApiClient;
import com.sportal365.articlescheduler.article.generation.infrastructure.client.model.*;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.FootballApiClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.constants.FootballConstants;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.SportSearchClient;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Service responsible for handling content creation and publishing operations.
 * This service orchestrates the process of converting generated article content
 * into publishable format and posting it to the content management system.
 *
 * Key responsibilities:
 * - Converting generated article structures to DTOs
 * - Creating and configuring content for publication
 * - Managing SEO settings and metadata
 * - Handling related content and tagging
 * - Integrating with external content APIs
 */
@Service
@AllArgsConstructor
public class ContentService {

    private final ContentApiClient contentApiClient;

    private final SportSearchClient sportSearchClient;
    private final FootballApiClient footballApiClient;

    /**
     * Posts a generated article to the content management system.
     * This is the main entry point for publishing generated articles. The method:
     * 1. Converts the generated article structure to a DTO
     * 2. Creates a ContentDto with all necessary metadata
     * 3. Configures SEO settings with automatic title and slug generation
     * 4. Sets up generic content metadata including template information
     * 5. Delegates to processGeneratedArticle for final publication
     *
     * @param generatedArticle the AI-generated article structure containing title, summary, and content
     * @param schedule the schedule containing publication settings, category, user info, and project domain
     * @param matchDetails the match information for creating related content and tags
     * @param editorBlocks the formatted content blocks ready for the content editor
     * @return the unique article ID assigned by the content management system
     */
    public String postArticle(ArticleStructure generatedArticle, Schedule schedule, MatchDetails matchDetails, List<ArticleContent.ArticleBlock> editorBlocks) {

        ArticleDTO article = buuildArticleDTO(generatedArticle);

        ContentDto contentDto = new ContentDto();
        contentDto.setSubtitle(article.getSummary());
        contentDto.setStrapline(article.getStrapline());
        contentDto.setTitle(article.getTitle());
        contentDto.setBody(editorBlocks);
        contentDto.setCategoryId(schedule.getCategory().getId());

        SeoDto seoDto = new SeoDto();
        seoDto.setTitle(article.getTitle());
        seoDto.setAutomaticSeoTitle(true);
        seoDto.setAutomaticSlug(true);
        contentDto.setSeo(seoDto);
        contentDto.setCreatedBy(schedule.getUserId());
        contentDto.setOriginSlug("ai");

        GenericContent genericContent = GenericContent.builder()
                .generatedFromTemplate(schedule.getTemplateName())
                .build();

        contentDto.setGeneric(genericContent);

        return processGeneratedArticle(contentDto, schedule.getProjectDomain(), matchDetails, schedule.getLanguage());
    }


    /**
     * Processes and publishes the generated article with related content and tags.
     * This method handles the final steps of article publication:
     * 1. Creates the article in the content management system
     * 2. Retrieves sport-related content connections
     * 3. Creates match-specific tags and related content
     * 4. Updates the article with all related content and tags
     *
     * @param articleDTO the content DTO containing all article data ready for publication
     * @param project the project domain identifier for content API routing
     * @param matchDetails the match information used for creating related content and tags
     * @param translationLanguage the language code for localized content and tags
     * @return the unique article ID assigned by the content management system
     */
    private String processGeneratedArticle(ContentDto articleDTO, String project, MatchDetails matchDetails,
                                           String translationLanguage) {
        String articleId = contentApiClient.createArticle(articleDTO, project).block();
        List<RelatedContentTag> relatedContent = sportSearchClient.getSportConnection(matchDetails, project,
                translationLanguage).block();
        if (relatedContent == null) {
            relatedContent = new ArrayList<>();
        }
        RelatedContentTag matchTag = RelatedContentTag.builder()
                .type("match")
                .provider(FootballConstants.FOOTBALL_PROVIDER)
                .data(footballApiClient.getMatchTagRelated(matchDetails, translationLanguage, project).block())
                .build();
        relatedContent.add(matchTag);
        contentApiClient.createUpdateRelatedContent(articleId, project, relatedContent).block();
        return articleId;
    }

    /**
     * Converts an ArticleStructure to an ArticleDTO for internal processing.
     * This method performs a simple mapping between the generated article structure
     * and the internal DTO format used by the service layer.
     *
     * @param articleStructure the generated article structure containing title, summary, strapline, and sections
     * @return ArticleDTO containing the mapped article data ready for content creation
     */
    private ArticleDTO buuildArticleDTO(ArticleStructure articleStructure) {
        return ArticleDTO.builder()
                .title(articleStructure.getTitle())
                .summary(articleStructure.getSummary())
                .strapline(articleStructure.getStrapline())
                .sections(articleStructure.getSections())
                .build();

    }
}
