package com.sportal365.articlescheduler.article.generation.application.service;

import com.sportal365.articlescheduler.shared.application.util.WidgetInputValidator;
import com.sportal365.articlescheduler.article.generation.domain.model.llm.ArticleStructure;
import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.shared.domain.model.Widget;
import com.sportal365.articlescheduler.shared.domain.model.enums.SectionType;
import com.sportal365.articlescheduler.article.generation.domain.model.template.PromptTemplate;
import com.sportal365.articlescheduler.shared.domain.widget.service.WidgetGenerationService;
import com.sportal365.articlescheduler.article.generation.infrastructure.client.model.ArticleContent;
import com.sportal365.articlescheduler.shared.infrastructure.persistence.repository.WidgetRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.*;

import static com.sportal365.articlescheduler.infrastructure.client.contentapi.constants.ContentApiConstants.*;

@Service
@AllArgsConstructor
@Slf4j
public class EditorBlockService {

    private final WidgetGenerationService widgetService;
    private final WidgetRepository widgetRepository;

    /**
     * Builds editor blocks from article structure, match details, and indexed sections
     *
     * @param articleStructure The article structure containing sections
     * @param matchDetails     The match details for widget data
     * @param indexedSections  The indexed sections indicating paragraphs and widgets
     * @param projectHeader    The project header for authentication
     * @param language         The language for content
     * @return A Mono with a list of article blocks
     */
    public List<ArticleContent.ArticleBlock> buildEditorBlocks(ArticleStructure articleStructure,
                                                               MatchDetails matchDetails,
                                                               List<PromptTemplate.IndexedSection> indexedSections,
                                                               String projectHeader,
                                                               String language) {
        List<ArticleContent.ArticleBlock> blocks = new ArrayList<>();
        indexedSections.sort(
                Comparator.comparingInt(o -> Integer.parseInt(o.getSection().getOrderNumber())));

        for (PromptTemplate.IndexedSection indexedSection : indexedSections) {
            if (SectionType.TEXT_PARAGRAPH.name().equals(indexedSection.getSection().getType())) {
                // Process text paragraphs
                indexedSection.getParagraphIndex()
                        .filter(index -> index < articleStructure.getSections().size())
                        .ifPresent(index -> {
                            String sectionId = UUID.randomUUID().toString().substring(0, 6);
                            ArticleStructure.Section section = articleStructure.getSections().get(index);

                            // Add section content
                            String contentId = UUID.randomUUID().toString().substring(0, 6);
                            blocks.add(createEditorBlock(
                                    contentId,
                                    String.format(PARAGRAPH_FORMAT, section.getContent()),
                                    PARAGRAPH_TYPE
                            ));
                        });
            } else {
                // Process widget sections
                String sectionId = indexedSection.getSection().getSectionId();
                Optional<Widget> widget = widgetRepository.findById(sectionId);
                if (widget.isEmpty() || !WidgetInputValidator.isWidgetInputValid(widget.get().getWidgetType(), matchDetails)) {
                    log.warn("Widget with ID {} not found or has insufficient input data", sectionId);
                    continue;
                }
                // Special handling for certain widget types
                Mono<ArticleContent.ArticleBlock> widgetBlockMono;

                // Use the standard method
                widgetBlockMono = widgetService.createWidgetBlock(
                        widget.get().getWidgetType(), matchDetails, projectHeader, language);

                blocks.add(widgetBlockMono.block());
            }
        }
        return blocks;
    }

    private ArticleContent.ArticleBlock createEditorBlock(String id, String content, String type) {
        return ArticleContent.ArticleBlock.builder()
                .id(id)
                .type(EDITOR_BLOCK_TYPE)
                .data(ArticleContent.BlockData.builder()
                        .content(content)
                        .placeholderName(id)
                        .type(type)
                        .build())
                .build();
    }
}
