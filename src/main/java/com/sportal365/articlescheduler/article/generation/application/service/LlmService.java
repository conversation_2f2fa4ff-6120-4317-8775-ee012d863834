package com.sportal365.articlescheduler.article.generation.application.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sportal365.articlescheduler.shared.domain.exception.ArticleNotGeneratedException;
import com.sportal365.articlescheduler.article.generation.domain.model.enums.ContentStatus;
import com.sportal365.articlescheduler.article.generation.domain.model.llm.ArticleStructure;
import com.sportal365.articlescheduler.article.generation.domain.model.llm.ContentRequestDTO;
import com.sportal365.articlescheduler.article.generation.domain.model.llm.ContentResponseDTO;
import com.sportal365.articlescheduler.article.generation.domain.model.ArticleExample;
import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.article.generation.domain.model.template.PromptTemplate;
import com.sportal365.articlescheduler.article.generation.infrastructure.client.LlmClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Service responsible for interacting with Large Language Model (LLM) providers to generate article content.
 * This service orchestrates the entire LLM interaction process including prompt preparation,
 * content generation, response processing, and example storage for future reference.
 *
 * Key responsibilities:
 * - Preparing and enriching prompt templates with schedule-specific parameters
 * - Communicating with external LLM clients for content generation
 * - Processing and validating LLM responses
 * - Storing successful article examples for training and reference
 * - Error handling and logging for LLM operations
 *
 * The service uses reactive programming patterns (Mono) to handle asynchronous LLM operations
 * and provides comprehensive error handling for various failure scenarios.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class LlmService {

    private final LlmClient llmClient;

    private final ObjectMapper objectMapper;

    private final MongoTemplate mongoTemplate;

    /**
     * Generates article content using a Large Language Model based on the provided template and context.
     * This is the main entry point for LLM-based article generation. The method:
     * 1. Enriches the prompt template with schedule-specific parameters
     * 2. Creates a content request DTO with all necessary data
     * 3. Calls the LLM client to generate content
     * 4. Validates the response status and processes successful completions
     * 5. Saves successful examples for future reference
     * 6. Handles errors and provides comprehensive logging
     *
     * @param filledPromptTemplate the template containing prompts and base parameters for content generation
     * @param matchDetails the match information providing context for the article
     * @param schedule the schedule containing generation settings, provider properties, and user preferences
     * @return Mono<ContentResponseDTO> containing the generated article content, metadata, and usage statistics
     * @throws ArticleNotGeneratedException if the LLM fails to complete the article generation
     * @throws RuntimeException if no article is generated despite successful LLM call
     */
    public Mono<ContentResponseDTO> generateArticle(
            PromptTemplate filledPromptTemplate, MatchDetails matchDetails, Schedule schedule) {

        PromptTemplate.Parameters parameters = addPromptParams(filledPromptTemplate.getParameters(), schedule);

        ContentRequestDTO requestDTO = new ContentRequestDTO(
        filledPromptTemplate.getPrompt(),
        objectMapper.convertValue(parameters, new TypeReference<>() {
        }),
                schedule.getProviderProperties()
        );

        return llmClient.generateContent(requestDTO)
                // 1) Log response but only create an ArticleDTO if content is COMPLETE
                .flatMap(response -> {
                    if (response == null || response.getStatus() != ContentStatus.COMPLETED) {
                        return Mono.error(new ArticleNotGeneratedException("Article not completed"));
                    }
                    saveArticleExample(response.getGeneratedContent(), matchDetails, parameters);
                    return Mono.just(response);
                })
                // 2) If the above pipeline returns empty, convert it to an error so the caller sees it
                .switchIfEmpty(Mono.error(new RuntimeException("No article was generated.")))

                // 3) Log any errors for debugging
                .doOnError(error -> log.error("Error while generating content: {}", error.getMessage(), error));
    }

    /**
     * Saves a successfully generated article example in the database for debugging purposes.
     * This method stores the generated article along with its context and parameters
     * in the article_examples database collection for debugging and analysis.
     * The saved examples can be accessed in production environments through dedicated
     * debug endpoints for troubleshooting and monitoring article generation quality.
     *
     * @param article the successfully generated article structure
     * @param matchDetails the match information that provided context for the generation
     * @param parameters the prompt parameters used during generation
     */
    private void saveArticleExample(ArticleStructure article, MatchDetails matchDetails, PromptTemplate.Parameters parameters) {
        ArticleExample articleExample = ArticleExample.builder()
                .article(article)
                .currentDate(Instant.now())
                .eventName(matchDetails.getEventName())
                .eventDate(matchDetails.getDate())
                .parameters(parameters)
                .build();

        mongoTemplate.save(articleExample, "article_examples");
    }

    /**
     * Enriches the prompt template parameters with schedule-specific settings.
     * This method extracts relevant configuration from the schedule and adds only
     * non-null parameters to the prompt template, allowing for customized article generation
     * based on user preferences and schedule settings. Null values are skipped to avoid
     * sending empty or undefined parameters to the LLM.
     *
     * @param parameters the base prompt template parameters to be enriched
     * @param schedule the schedule containing user preferences and generation settings
     * @return PromptTemplate.Parameters enriched with schedule-specific non-null parameters
     */
    private PromptTemplate.Parameters addPromptParams(PromptTemplate.Parameters parameters, Schedule schedule) {
        List<PromptTemplate.ScheduleParameters> scheduleParamsList = new ArrayList<>();

        addParameterIfNotNull(scheduleParamsList, "generate_strapline", schedule.getGenerateStrapline());
        addParameterIfNotNull(scheduleParamsList, "generate_summary", schedule.getGenerateSummary());
        addParameterIfNotNull(scheduleParamsList, "sport", schedule.getSport());
        addParameterIfNotNull(scheduleParamsList, "llmTemperature", schedule.getLlmTemperature());

        parameters.setScheduleParameters(scheduleParamsList);

        return parameters;
    }

    /**
     * Conditionally adds a parameter to the parameters list if the value is not null.
     * This utility method helps build the schedule parameters list by only including
     * parameters that have actual values, avoiding null or empty parameters in the prompt.
     *
     * @param paramsList the list of schedule parameters to add to
     * @param paramName the name of the parameter to add
     * @param value the value of the parameter (only added if not null)
     */
    private void addParameterIfNotNull(List<PromptTemplate.ScheduleParameters> paramsList, String paramName, Object value) {
        if (value != null) {
            paramsList.add(createParam(paramName, value));
        }
    }

    /**
     * Creates a ScheduleParameters object with the specified name and value.
     * This utility method provides a clean way to create parameter objects
     * for the prompt template system.
     *
     * @param paramName the name of the parameter
     * @param value the value of the parameter
     * @return PromptTemplate.ScheduleParameters object with the specified name and value
     */
    private static PromptTemplate.ScheduleParameters createParam(String paramName, Object value) {
        PromptTemplate.ScheduleParameters param = new PromptTemplate.ScheduleParameters();
        param.setName(paramName);
        param.setValue(value);
        return param;
    }
}
