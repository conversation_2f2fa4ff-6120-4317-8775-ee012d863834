package com.sportal365.articlescheduler.article.generation.domain.model.llm;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents the structure of an article with sections, summary, and strapline
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArticleStructure {
    private String title;
    private String summary;
    private String strapline;

    @Builder.Default
    private List<Section> sections = new ArrayList<>();

    /**
     * Represents a section in the article with a title and content
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Section {
        private String title;
        private String content;
    }
}
