package com.sportal365.articlescheduler.article.generation.domain.model.llm;

import com.sportal365.articlescheduler.article.generation.domain.model.enums.ContentStatus;
import lombok.Builder;
import lombok.Data;
import lombok.Value;

@Data
@Builder
public class ContentResponseDTO {
    ArticleStructure generatedContent;
    ContentStatus status;
    PromptMetadata promptMetadata;

    @Value
    @Builder
    public static class PromptMetadata {
        String id;
        String model;
        RateLimit rateLimit;
        Usage usage;
        boolean empty;

        @Value
        @Builder
        public static class RateLimit {
            int requestsRemaining;
            int tokensRemaining;
            String tokensReset;
            int requestsLimit;
            String requestsReset;
            int tokensLimit;
        }

        @Value
        @Builder
        public static class Usage {
            int generationTokens;
            int promptTokens;
            int totalTokens;
        }
    }
}
