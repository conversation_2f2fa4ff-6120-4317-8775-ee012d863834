package com.sportal365.articlescheduler.article.generation.domain.model.template;

import com.sportal365.articlescheduler.schedules.infrastructure.persistence.entity.TemplateDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
public class PromptTemplate {

    private String prompt;
    private Parameters parameters;
    private ProviderProperties providerProperties;
    private List<IndexedSection> indexedSections;

    @Data
    public static class Parameters {
        private Structure structure;
        private KeyConsiderations keyConsiderations;
        private List<String> exclusions;
        private Map<String, Object> matchDetails;
        private Output output;
        private List<ScheduleParameters> scheduleParameters;
    }

    @Data
    public static class Structure {
        private String title;
        private List<String> sections;
    }

    @Data
    public static class KeyConsiderations {
        private String directOutput;
        private String journalisticTone;
        private String terminology;
        private String logicalFlow;
    }

    @Data
    public static class Output {
        private String format;
        private String language;
    }

    @Data
    public static class ScheduleParameters {
        private String name;
        private Object value;
    }

    @Data
    @AllArgsConstructor
    public static class ProviderProperties {
        private String provider;
        private String model;
    }

    @Data
    @Builder
    public static class IndexedSection {

        private TemplateDocument.Section section;

        /**
         * Only set for paragraph types, it is used to correctly fetch the section from the LLM response
         */
        private Integer paragraphIndex;

        public Optional<Integer> getParagraphIndex() {
            return Optional.ofNullable(paragraphIndex);
        }
    }
}
