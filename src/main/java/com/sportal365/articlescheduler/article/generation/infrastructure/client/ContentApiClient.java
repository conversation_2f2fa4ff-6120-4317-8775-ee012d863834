package com.sportal365.articlescheduler.article.generation.infrastructure.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sportal365.articlescheduler.shared.domain.exception.ContentCantBePostedException;
import com.sportal365.articlescheduler.article.generation.infrastructure.client.model.ContentDto;
import com.sportal365.articlescheduler.article.generation.infrastructure.client.model.RelatedContentTag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;

import static com.sportal365.articlescheduler.infrastructure.client.contentapi.constants.ContentApiConstants.*;


@Component
@Slf4j
@RequiredArgsConstructor
public class ContentApiClient {

    private String cachedToken = null;
    private final AuthenticationService authenticationService;
    private final WebClient contentApiWebClient;

    private Mono<String> getToken() {
        if (cachedToken != null) {
            return Mono.just(cachedToken);
        }

        return authenticationService.getAccessToken()
                .doOnNext(token -> cachedToken = token)
                .doOnError(error -> log.error("Error getting token: {}", error.getMessage()));
    }

    public Mono<String> createArticle(ContentDto contentDto, String project) {
        return getToken()
                .switchIfEmpty(Mono.error(new RuntimeException("Failed to get access token")))
                .doOnNext(token -> log.info("Got token successfully, proceeding with article creation"))
                .flatMap(token -> contentApiWebClient.post()
                        .uri(ARTICLES_ENDPOINT)
                        .header(AUTHORIZATION, token)
                        .header(X_PROJECT, project)
                        .header(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                        .bodyValue(contentDto)
                        .retrieve()
                        .onStatus(HttpStatusCode::is5xxServerError, response -> {
                            log.error("Server error occurred. Status: {}", response.statusCode());
                            return response.bodyToMono(String.class)
                                    .flatMap(errorBody -> Mono.error(new ContentCantBePostedException("Server error: " + errorBody)));
                        })
                        .onStatus(HttpStatusCode::is4xxClientError, response -> {
                            log.error("Client error occurred. Status: {}", response.statusCode());
                            return response.bodyToMono(String.class)
                                    .flatMap(errorBody -> Mono.error(new ContentCantBePostedException("Server error: " + errorBody)));
                        })
                        .bodyToMono(String.class)
                        .<String>handle((responseBody, sink) -> {
                            try {
                                ObjectMapper mapper = new ObjectMapper();
                                JsonNode jsonNode = mapper.readTree(responseBody);
                                String createdId = jsonNode.path("data").path("id").asText();
                                if (createdId != null && !createdId.isEmpty()) {
                                    log.info("Successfully extracted article ID: {}", createdId);
                                    sink.next(createdId);
                                    return;
                                }
                                log.error("Created content ID not found in response");
                                sink.error(new RuntimeException("Created content ID not found in response"));
                            } catch (JsonProcessingException e) {
                                log.error("Error parsing response body: {}. Raw response: {}",
                                        e.getMessage(), responseBody);
                                sink.error(new RuntimeException("Error parsing response", e));
                            }
                        })
                        .onErrorMap(e -> {
                            String requestDetails = String.format(
                                    "Error posting content to %s for project: %s, content title: %s",
                                    ARTICLES_ENDPOINT,
                                    project,
                                    contentDto.getTitle() != null ? contentDto.getTitle() : "unknown"
                            );

                            log.error("{}: {}", requestDetails, e.getMessage(), e);

                            if (e instanceof ContentCantBePostedException) {
                                return e;
                            }

                            return new ContentCantBePostedException(
                                    String.format("%s. Original error: %s", requestDetails, e.getMessage())
                            );
                        }));
    }

    public Mono<Boolean> createUpdateRelatedContent(String contentId, String project, List<RelatedContentTag> tags) {
        return getToken()
                .switchIfEmpty(Mono.error(new RuntimeException("Failed to get access token")))
                .flatMap(token -> contentApiWebClient.post()
                        .uri(String.format(RELATED_CONTENT_ENDPOINT, contentId))
                        .header(AUTHORIZATION, token)
                        .header(X_PROJECT, project)
                        .header(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON)
                        .bodyValue(tags)
                        .exchangeToMono(clientResponse -> {
                            if (clientResponse.statusCode().equals(HttpStatus.CREATED) ||
                                    clientResponse.statusCode().equals(HttpStatus.OK)) {
                                log.info("Successfully created related content tags for contentId: {}", contentId);
                                return Mono.just(true);
                            } else {
                                return clientResponse.bodyToMono(String.class)
                                        .flatMap(errorBody -> {
                                            log.error("Failed to create related content for contentId: {}. Status: {}, Response: {}",
                                                    contentId, clientResponse.statusCode(), errorBody);
                                            return clientResponse.createException()
                                                    .flatMap(Mono::error);
                                        });
                            }
                        })
                        .doOnError(error -> log.error("Error creating related content tags for contentId: {}. Error: {}",
                                contentId, error.getMessage(), error)));
    }

}
