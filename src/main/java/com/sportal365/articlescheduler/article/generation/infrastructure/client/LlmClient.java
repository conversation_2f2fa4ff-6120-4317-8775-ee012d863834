package com.sportal365.articlescheduler.article.generation.infrastructure.client;

import com.sportal365.articlescheduler.article.generation.domain.model.llm.ContentRequestDTO;
import com.sportal365.articlescheduler.article.generation.domain.model.llm.ContentResponseDTO;
import com.sportal365.articlescheduler.shared.infrastructure.constant.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Component
@Slf4j
@RequiredArgsConstructor
public class LlmClient {

    private final WebClient llmWebClient;

    public Mono<ContentResponseDTO> generateContent(ContentRequestDTO requestDTO) {
        log.info("Generate content request");
        try {
            return llmWebClient.post()
                    .uri(Constants.CONTENT_GENERATE_URI)
                    .bodyValue(requestDTO)
                    .retrieve()
                    .bodyToMono(ContentResponseDTO.class)
                    .doOnNext(this::logResponseSync)
                    .doOnError(this::logContentGenerationError);
        } catch (Exception e) {
            logContentGenerationError(e);
            throw new RuntimeException("Failed to generate content", e);
        }
    }

    private void logResponseSync(ContentResponseDTO response) {
        if (response != null) {
            log.debug("Received response: {}", response);
        } else {
            log.warn("Received null response from LLM service.");
        }
    }

    private void logContentGenerationError(Throwable t) {
        log.error("Error during content generation: {}", t.getMessage(), t);
    }
}
