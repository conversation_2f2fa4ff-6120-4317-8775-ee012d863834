package com.sportal365.articlescheduler.article.generation.infrastructure.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContentDto {

    private String id;

    private String title;

    private String subtitle;

    private List<ArticleContent.ArticleBlock> body;

    private String strapline;

    private String footer;

    private String status;

    private String type;

    @JsonProperty("custom_author")
    private String customAuthor;

    private SeoDto seo;

    @JsonProperty("origin_slug")
    private String originSlug;

    private String language;

    private String source;

    @JsonProperty("category_id")
    private String categoryId;

    @JsonProperty("additional_categories")
    private List<Integer> additionalCategories;

    @JsonProperty("created_by")
    private String createdBy;

    private GenericContent generic;

}

