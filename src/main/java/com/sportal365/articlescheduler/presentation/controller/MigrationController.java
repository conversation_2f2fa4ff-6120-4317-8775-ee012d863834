package com.sportal365.articlescheduler.presentation.controller;

import com.sportal365.articlescheduler.shared.domain.exception.MigrationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Controller for manual migration operations
 * Allows triggering migrations and rollbacks by service name
 */
@RestController
@RequestMapping("/api/migrations")
@Slf4j
public class MigrationController {

    private final ApplicationContext applicationContext;

    @Autowired
    public MigrationController(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * Execute a migration by service name
     * @param serviceName The bean name of the migration service (e.g. "scheduleIdMigrationWithRollbackService")
     * @return Migration result
     */
    @PostMapping("/execute/{serviceName}")
    public ResponseEntity<Map<String, Object>> executeMigration(@PathVariable String serviceName) {
        log.info("Received request to execute migration: {}", serviceName);

        try {
            Object migrationService = applicationContext.getBean(serviceName);

            Method migrateMethod = findMethod(migrationService, "migrate");

            if (migrateMethod == null) {
                throw new MigrationException("No suitable migration method found on service: " + serviceName);
            }

            log.info("Executing migration method: {}.{}", serviceName, migrateMethod.getName());

            return getMapResponseEntity(serviceName, migrationService, migrateMethod);

        } catch (Exception e) {
            log.error("Error executing migration {}: {}", serviceName, e.getMessage(), e);
            throw new MigrationException("Error executing migration: " + e.getMessage(), e);
        }
    }

    /**
     * Execute a rollback by service name
     * @param serviceName The bean name of the migration service (e.g. "scheduleIdMigrationWithRollbackService")
     * @return Rollback result
     */
    @PostMapping("/rollback/{serviceName}")
    public ResponseEntity<Map<String, Object>> executeRollback(@PathVariable String serviceName) {
        log.info("Received request to execute rollback for migration: {}", serviceName);

        try {
            Object migrationService = applicationContext.getBean(serviceName);

            Method rollbackMethod = findMethod(migrationService, "rollback");

            if (rollbackMethod == null) {
                throw new MigrationException("No suitable rollback method found on service: " + serviceName);
            }

            log.info("Executing rollback method: {}.{}", serviceName, rollbackMethod.getName());

            return getMapResponseEntity(serviceName, migrationService, rollbackMethod);

        } catch (Exception e) {
            log.error("Error executing rollback {}: {}", serviceName, e.getMessage(), e);
            throw new MigrationException("Error executing rollback: " + e.getMessage(), e);
        }
    }

    private ResponseEntity<Map<String, Object>> getMapResponseEntity(@PathVariable String serviceName, Object migrationService, Method rollbackMethod) throws IllegalAccessException, InvocationTargetException {
        Object result = rollbackMethod.invoke(migrationService);

        Map<String, Object> response = new HashMap<>();
        response.put("timestamp", LocalDateTime.now().toString());
        response.put("status", "success");
        response.put("service", serviceName);
        response.put("method", rollbackMethod.getName());
        response.put("result", result.toString());

        return ResponseEntity.ok(response);
    }

    /**
     * List all available migration services
     * @return List of available migration services
     */
    @GetMapping("/services")
    public ResponseEntity<Map<String, Object>> listMigrationServices() {
        Map<String, Object> response = new HashMap<>();
        Map<String, String> services = new HashMap<>();

        String[] beanNames = applicationContext.getBeanNamesForType(Object.class);
        for (String beanName : beanNames) {
            if (beanName.toLowerCase().contains("migration")) {
                Object bean = applicationContext.getBean(beanName);
                Class<?> beanClass = bean.getClass();

                Method migrateMethod = findMethod(bean, "migrate");
                Method rollbackMethod = findMethod(bean, "rollback");

                if (migrateMethod != null || rollbackMethod != null) {
                    services.put(beanName, beanClass.getSimpleName());
                }
            }
        }

        response.put("services", services);
        response.put("count", services.size());

        return ResponseEntity.ok(response);
    }

    /**
     * Helper method to find a method by possible names
     */
    private Method findMethod(Object object, String... methodNames) {
        Class<?> clazz = object.getClass();

        for (String methodName : methodNames) {
            try {
                try {
                    return clazz.getMethod(methodName);
                } catch (NoSuchMethodException e) {
                    // Method not found, continue checking
                }
            } catch (Exception e) {
                log.debug("Error finding method {}: {}", methodName, e.getMessage());
            }
        }

        return null;
    }
}