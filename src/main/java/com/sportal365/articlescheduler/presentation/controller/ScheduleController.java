package com.sportal365.articlescheduler.presentation.controller;

import com.sportal365.articlescheduler.shared.application.dto.common.PageRequest;
import com.sportal365.articlescheduler.schedules.application.dto.request.ScheduleListRequest;
import com.sportal365.articlescheduler.schedules.application.dto.response.ScheduleCountResponse;
import com.sportal365.articlescheduler.schedules.domain.model.mappers.ScheduleMapper;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.schedules.application.dto.request.ScheduleCreateRequest;
import com.sportal365.articlescheduler.schedules.application.dto.request.ScheduleUpdateRequest;
import com.sportal365.articlescheduler.shared.application.dto.common.response.PageResponse;
import com.sportal365.articlescheduler.schedules.application.dto.response.ScheduleResponse;
import com.sportal365.articlescheduler.schedules.application.service.ScheduleService;
import com.sportal365.articlescheduler.shared.domain.utils.DateUtils;
import com.sportal365.articlescheduler.schedules.domain.validator.ScheduleRequestValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;

import static com.sportal365.articlescheduler.presentation.constant.Constants.X_PROJECT;

@RestController
@RequestMapping("/schedules")
@RequiredArgsConstructor
@Slf4j
public class ScheduleController {
    private final ScheduleService scheduleService;
    private final ScheduleMapper scheduleMapper;
    private final ScheduleRequestValidator validator;

    @PostMapping
    public ResponseEntity<List<ScheduleResponse>> createSchedule(
            @RequestBody @Validated ScheduleCreateRequest request,
            @RequestHeader(X_PROJECT) String projectDomain
    ) {

        validator.validate(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(scheduleService.createSchedule(request, projectDomain));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ScheduleResponse> getSchedule(
            @PathVariable String id,
            //This header stays here because of swagger
            @RequestHeader(X_PROJECT) String projectDomain
    ) {
        Schedule schedule = scheduleService.getSchedule(id, projectDomain);
        return ResponseEntity.ok(scheduleMapper.toResponse(schedule));
    }

    @GetMapping
    public ResponseEntity<PageResponse<ScheduleResponse>> listSchedules(
            @RequestHeader(X_PROJECT) String projectDomain,
            @RequestParam(value = "category_id", required = false) String categoryName,
            @RequestParam(value = "event_name", required = false) String eventName,
            @RequestParam(value = "competition_id", required = false) String competitionName,
            @RequestParam(value = "from_date", required = false) String startDateStr,
            @RequestParam(value = "to_date", required = false) String endDateStr,
            @RequestParam(value = "created_by", required = false) String createdBy,
            @RequestParam(value = "time_zone", required = false) String timeZone,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size
    ) {
        Instant startDate = DateUtils.getDate(startDateStr, timeZone);
        Instant endDate = DateUtils.getDate(endDateStr, timeZone);

        ScheduleListRequest listRequest = ScheduleListRequest.builder()
                .categoryName(categoryName)
                .eventName(eventName)
                .competitionName(competitionName)
                .from(startDate)
                .to(endDate)
                .createdBy(createdBy)
                .timeZone(timeZone)
                .status(status)
                .pageRequest(PageRequest.builder().page(page - 1).size(size).build())
                .build();

        validator.validateListRequest(listRequest);
        Page<Schedule> schedulePage = scheduleService.listSchedules(
                projectDomain, listRequest);

        Page<ScheduleResponse> scheduleResponsePage = schedulePage.map(scheduleMapper::toResponse);

        PageResponse<ScheduleResponse> response = PageResponse.from(scheduleResponsePage);
        return ResponseEntity.ok(response);
    }

    @PatchMapping("/{id}")
    public ResponseEntity<ScheduleResponse> updateSchedule(
            @PathVariable String id,
            @RequestBody @Validated ScheduleUpdateRequest request,
            @RequestHeader(X_PROJECT) String projectDomain
    ) {
        Schedule schedule = scheduleService.updateSchedule(id, request, projectDomain);
        return ResponseEntity.ok(scheduleMapper.toResponse(schedule));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSchedule(
            @PathVariable String id,
            @RequestHeader(X_PROJECT) String projectDomain
    ) {
        scheduleService.deleteSchedule(id, projectDomain);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/count")
    public ResponseEntity<ScheduleCountResponse> schedulesCount(
            @RequestHeader(X_PROJECT) String projectDomain
    ) {
        Integer count = scheduleService.schedulesCount(projectDomain);
        return ResponseEntity.ok(new ScheduleCountResponse(count));
    }
}