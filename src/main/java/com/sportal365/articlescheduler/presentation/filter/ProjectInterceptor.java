package com.sportal365.articlescheduler.presentation.filter;

import com.sportal365.articlescheduler.shared.domain.exception.MissingProjectHeaderException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.web.servlet.HandlerInterceptor;

public class ProjectInterceptor implements HandlerInterceptor {

    private static final String PROJECT_HEADER = "X-Project";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        response.setHeader(HttpHeaders.VARY, PROJECT_HEADER);
        if (!HttpMethod.OPTIONS.matches(request.getMethod()) && (request.getHeader(PROJECT_HEADER) == null || request.getHeader(PROJECT_HEADER).isEmpty())) {
            throw new MissingProjectHeaderException(String.format("%s header is mandatory!", PROJECT_HEADER));
        }
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }
}
