package com.sportal365.articlescheduler.schedules.application.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sportal365.articlescheduler.schedules.domain.model.enums.ScheduleStatus;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScheduleResponse {

    @JsonProperty("id")
    private String id;

    @JsonProperty("match_name")
    private String matchName;

    @JsonProperty("match_date")
    private ZonedDateTime matchDate;

    @JsonProperty("generation_time")
    private ZonedDateTime generationTime;

    @JsonProperty("article_id")
    private String articleId;

    @JsonProperty("created_by")
    private String userName;

    @JsonProperty("competition_name")
    private String competitionName;

    @JsonProperty("sport")
    private String sport;

    @JsonProperty("message")
    private String message;

    @JsonProperty("category_name")
    private String categoryName;

    @JsonProperty("status")
    private ScheduleStatus status;

    @JsonProperty("template_name")
    private String templateName;
}
