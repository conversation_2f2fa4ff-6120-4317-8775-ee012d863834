package com.sportal365.articlescheduler.schedules.application.dto.template;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sportal365.articlescheduler.shared.application.dto.common.Sport;
import com.sportal365.articlescheduler.shared.application.dto.common.TemplateStatus;
import com.sportal365.articlescheduler.shared.domain.model.enums.SectionType;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;
import java.util.Map;

@Data
public class TemplateDto {
    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("sport")
    private Sport sport;

    @JsonProperty("template_type")
    private String templateType;

    @JsonProperty("status")
    private TemplateStatus status;

    @JsonProperty("prompt_template")
    private PromptTemplate promptTemplate;

    @Data
    public static class PromptTemplate {
        @JsonProperty("prompt")
        private String prompt;

        @JsonProperty("parameters")
        private Parameters parameters;
    }

    @Data
    public static class Parameters {
        @JsonProperty("structure")
        private Structure structure;

        @JsonProperty("key_considerations")
        private KeyConsiderations keyConsiderations;

        @JsonProperty("exclusions")
        private List<String> exclusions;

        @JsonProperty("match_details")
        private Map<String, Object> matchDetails;

        @JsonProperty("output")
        private Output output;
    }

    @Data
    public static class Structure {
        @JsonProperty("title")
        private String title;

        @JsonProperty("summary")
        private String summary;

        @JsonProperty("sections")
        private List<Section> sections;
    }

    @Data
    public static class KeyConsiderations {
        @JsonProperty("direct_output")
        private String directOutput;

        @JsonProperty("journalistic_tone")
        private String journalisticTone;

        @JsonProperty("terminology")
        private String terminology;

        @JsonProperty("logical_flow")
        private String logicalFlow;
    }

    @Data
    public static class Output {
        @JsonProperty("format")
        private String format;
    }

    @Data
    public static class Section {
        @JsonProperty("section_id")
        private String sectionId;
        @JsonProperty("order_number")
        private String orderNumber;
        @JsonProperty("type")
        private SectionType type;
    }
}
