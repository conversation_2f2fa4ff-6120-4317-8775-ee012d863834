package com.sportal365.articlescheduler.schedules.application.service;

import com.sportal365.articlescheduler.schedules.application.dto.template.TemplateDto;
import com.sportal365.articlescheduler.shared.domain.exception.NotFoundException;
import com.sportal365.articlescheduler.schedules.domain.model.ParagraphTemplate;
import com.sportal365.articlescheduler.schedules.application.service.ParagraphTemplateService;
import com.sportal365.articlescheduler.schedules.domain.model.Template;
import com.sportal365.articlescheduler.shared.domain.model.Widget;
import com.sportal365.articlescheduler.shared.domain.widget.service.WidgetService;
import com.sportal365.articlescheduler.shared.domain.model.enums.SectionType;
import com.sportal365.articlescheduler.shared.domain.model.enums.TemplateTypeEnum;
import com.sportal365.articlescheduler.shared.infrastructure.events.DefaultTemplateCreatedEvent;
import com.sportal365.articlescheduler.schedules.infrastructure.persistence.entity.TemplateDocument;
import com.sportal365.articlescheduler.schedules.infrastructure.persistence.entity.TemplateId;
import com.sportal365.articlescheduler.schedules.infrastructure.persistence.repository.TemplateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TemplateService {

    private final TemplateRepository templateRepository;
    private final ParagraphTemplateService paragraphTemplateService;
    private final WidgetService widgetService;
    private final ApplicationEventPublisher eventPublisher;

    @Value("${default.template.project}")
    private String DEFAULT_TEMPLATE_PROJECT;

    @Transactional
    public Template createTemplate(TemplateDto dto, String projectDomain) {
        TemplateTypeEnum type = TemplateTypeEnum.of(dto.getTemplateType());
        if (type == null) {
            throw new IllegalArgumentException("Invalid articleType: " + dto.getTemplateType());
        }
        TemplateId templateId = TemplateId.builder()
                .templateType(type)
                .templateName(dto.getName())
                .projectDomain(projectDomain)
                .build();
        if (templateRepository.findById(templateId).isPresent()) {
            throw new IllegalArgumentException("Template with name: " + dto.getName() + " already exists");
        }

        TemplateDocument.PromptTemplate promptTemplate = convertDtoToDocumentPromptTemplate(dto.getPromptTemplate());

        TemplateDocument templateDocument = TemplateDocument.builder()
                .id(templateId)
                .sport(dto.getSport().toString())
                .createdAt(Instant.now())
                .description(dto.getDescription())
                .status("ACTIVE")
                .promptTemplate(promptTemplate)
                .build();

        TemplateDocument document = templateRepository.save(templateDocument);
        publishEventForDefaultProject(projectDomain);

        return toDomain(document);
    }

    public Template getTemplate(String projectDomain, String templateType, String templateName) {
        TemplateTypeEnum type = TemplateTypeEnum.of(templateType);
        if (type == null) {
            throw new IllegalArgumentException("Invalid templateType: " + templateType);
        }
        TemplateId id = TemplateId.builder()
                .projectDomain(projectDomain)
                .templateType(type)
                .templateName(templateName)
                .build();
        Optional<TemplateDocument> document = templateRepository.findById(id);
        if (document.isEmpty()) {
            throw new NotFoundException("Template with projectDomain: " + projectDomain +
                    " and templateType: " + templateType + " not found");
        }
        return toDomain(document.get());
    }

    public List<Template> listTemplates(String projectDomain) {
        return templateRepository.findAllById_ProjectDomain(projectDomain)
                .stream()
                .map(this::toDomain)
                .collect(Collectors.toList());
    }

    public Template updateTemplate(TemplateDto templateDto, String projectDomain, String templateName, String templateType) {
        TemplateId id = TemplateId.builder()
                .projectDomain(projectDomain)
                .templateType(TemplateTypeEnum.of(templateType))
                .templateName(templateName)
                .build();
        Optional<TemplateDocument> templateDocument = templateRepository
                .findById(id);
        if (templateDocument.isEmpty()) {
            throw new NotFoundException("Template with projectDomain: " + projectDomain +
                    " articleType: " + templateDto.getTemplateType() + " and name: " + templateDto.getName() + " not found");
        }
        TemplateDocument document = templateDocument.get();
        if (templateDto.getName() != null) {
            document.getId().setTemplateName(templateDto.getName());
        }
        if (templateDto.getDescription() != null) {
            document.setDescription(templateDto.getDescription());
        }
        if (templateDto.getSport() != null) {
            document.setSport(templateDto.getSport().toString());
        }
        if (templateDto.getStatus() != null) {
            document.setStatus(templateDto.getStatus().toString());
        }
        if (templateDto.getPromptTemplate() != null) {
            document.setPromptTemplate(convertDtoToDocumentPromptTemplate(templateDto.getPromptTemplate()));
        }

        document.setUpdatedAt(Instant.now());

        templateRepository.save(document);
        publishEventForDefaultProject(projectDomain);
        return toDomain(document);
    }

    private void publishEventForDefaultProject(String projectDomain) {
        if (projectDomain.equals(DEFAULT_TEMPLATE_PROJECT)) {
            eventPublisher.publishEvent(new DefaultTemplateCreatedEvent());
        }
    }

    public void deleteTemplate(String articleType, String projectDomain, String templateName) {
        TemplateId id = TemplateId.builder()
                .projectDomain(projectDomain)
                .templateType(TemplateTypeEnum.of(articleType))
                .templateName(templateName)
                .build();
        Optional<TemplateDocument> document = templateRepository.findById(id);
        if (document.isEmpty()) {
            throw new NotFoundException("Template with articleType: " +
                    articleType + " not found");
        }
        templateRepository.delete(document.get());
    }

    public void createDefaultTemplates(String projectDomain) {
        List<TemplateDocument> templates = templateRepository.findAllById_ProjectDomain(DEFAULT_TEMPLATE_PROJECT);
        List<TemplateDocument> templatesForSaving = processTemplates(templates, projectDomain);

        if (!templatesForSaving.isEmpty()) {
            templateRepository.saveAll(templatesForSaving);
        }
    }

    /**
     * Process default templates and create or update project-specific templates.
     *
     * @param defaultTemplates The list of default templates
     * @param projectDomain    The project domain to create templates for
     * @return List of templates that need to be saved
     */
    private List<TemplateDocument> processTemplates(List<TemplateDocument> defaultTemplates, String projectDomain) {
        List<TemplateDocument> templatesForSaving = new ArrayList<>();

        defaultTemplates.forEach(defaultTemplate -> {
            TemplateId newId = createTemplateId(defaultTemplate, projectDomain);
            Optional<TemplateDocument> existingTemplate = templateRepository.findById(newId);

            if (existingTemplate.isPresent()) {
                processExistingTemplate(existingTemplate.get(), defaultTemplate, templatesForSaving);
            } else {
                templatesForSaving.add(createNewTemplate(defaultTemplate, newId));
            }
        });

        return templatesForSaving;
    }

    /**
     * Create a new template ID for the specified project domain.
     */
    private TemplateId createTemplateId(TemplateDocument template, String projectDomain) {
        return TemplateId.builder()
                .projectDomain(projectDomain)
                .templateType(template.getId().getTemplateType())
                .templateName(template.getId().getTemplateName())
                .build();
    }

    /**
     * Process an existing template by checking if it needs to be updated.
     */
    private void processExistingTemplate(TemplateDocument existing, TemplateDocument defaultTemplate, List<TemplateDocument> templatesForSaving) {
        if (hasContentChanges(existing, defaultTemplate)) {
            updateExistingTemplate(existing, defaultTemplate);
            templatesForSaving.add(existing);
        }
    }

    /**
     * Check if there are content changes between templates (ignoring project domain).
     */
    private boolean hasContentChanges(TemplateDocument existing, TemplateDocument defaultTemplate) {
        return !Objects.equals(existing.getSport(), defaultTemplate.getSport()) ||
                !Objects.equals(existing.getDescription(), defaultTemplate.getDescription()) ||
                !Objects.equals(existing.getStatus(), defaultTemplate.getStatus()) ||
                !Objects.equals(existing.getPromptTemplate(), defaultTemplate.getPromptTemplate()) ||
                !Objects.equals(existing.getId(), defaultTemplate.getId());
    }

    /**
     * Update an existing template with values from the default template.
     */
    private void updateExistingTemplate(TemplateDocument existing, TemplateDocument defaultTemplate) {
        existing.setSport(defaultTemplate.getSport());
        existing.setDescription(defaultTemplate.getDescription());
        existing.setStatus(defaultTemplate.getStatus());
        existing.setPromptTemplate(defaultTemplate.getPromptTemplate());
    }

    /**
     * Create a new template based on the default template.
     */
    private TemplateDocument createNewTemplate(TemplateDocument defaultTemplate, TemplateId newId) {
        return TemplateDocument.builder()
                .id(newId)
                .sport(defaultTemplate.getSport())
                .createdAt(Instant.now())
                .description(defaultTemplate.getDescription())
                .status(defaultTemplate.getStatus())
                .promptTemplate(defaultTemplate.getPromptTemplate())
                .build();
    }

    /**
     * Converts a DTO PromptTemplate to a Document PromptTemplate
     */
    private TemplateDocument.PromptTemplate convertDtoToDocumentPromptTemplate(TemplateDto.PromptTemplate dtoPromptTemplate) {
        if (dtoPromptTemplate == null) {
            return null;
        }

        TemplateDocument.PromptTemplate.PromptTemplateBuilder promptBuilder =
                TemplateDocument.PromptTemplate.builder();

        promptBuilder.prompt(dtoPromptTemplate.getPrompt());

        if (dtoPromptTemplate.getParameters() != null) {
            TemplateDocument.Parameters.ParametersBuilder paramsBuilder =
                    TemplateDocument.Parameters.builder();

            if (dtoPromptTemplate.getParameters().getStructure() != null) {
                TemplateDto.Structure dtoStructure = dtoPromptTemplate.getParameters().getStructure();
                TemplateDocument.Structure.StructureBuilder structureBuilder =
                        TemplateDocument.Structure.builder()
                                .title(dtoStructure.getTitle())
                                .summary(dtoStructure.getSummary());

                if (dtoStructure.getSections() != null) {
                    List<TemplateDocument.Section> sections = dtoStructure.getSections().stream()
                            .map(s -> TemplateDocument.Section.builder()
                                    .sectionId(s.getSectionId())
                                    .orderNumber(s.getOrderNumber())
                                    .type(s.getType().toString())
                                    .build())
                            .collect(Collectors.toList());
                    structureBuilder.sections(sections);
                }

                paramsBuilder.structure(structureBuilder.build());
            }

            if (dtoPromptTemplate.getParameters().getKeyConsiderations() != null) {
                TemplateDto.KeyConsiderations dtoConsiderations =
                        dtoPromptTemplate.getParameters().getKeyConsiderations();

                TemplateDocument.KeyConsiderations considerations =
                        TemplateDocument.KeyConsiderations.builder()
                                .directOutput(dtoConsiderations.getDirectOutput())
                                .journalisticTone(dtoConsiderations.getJournalisticTone())
                                .terminology(dtoConsiderations.getTerminology())
                                .logicalFlow(dtoConsiderations.getLogicalFlow())
                                .build();

                paramsBuilder.keyConsiderations(considerations);
            }

            paramsBuilder.exclusions(dtoPromptTemplate.getParameters().getExclusions());
            paramsBuilder.matchDetails(dtoPromptTemplate.getParameters().getMatchDetails());

            if (dtoPromptTemplate.getParameters().getOutput() != null) {
                TemplateDocument.Output output = TemplateDocument.Output.builder()
                        .format(dtoPromptTemplate.getParameters().getOutput().getFormat())
                        .build();

                paramsBuilder.output(output);
            }

            promptBuilder.parameters(paramsBuilder.build());
        }

        return promptBuilder.build();
    }

    private Template toDomain(TemplateDocument document) {
        Template.PromptTemplate promptTemplate = null;

        if (document.getPromptTemplate() != null) {
            Template.PromptTemplate.PromptTemplateBuilder promptTemplateBuilder = Template.PromptTemplate.builder();

            if (document.getPromptTemplate().getPrompt() != null) {
                promptTemplateBuilder.prompt(document.getPromptTemplate().getPrompt());
            }

            if (document.getPromptTemplate().getParameters() != null &&
                    document.getPromptTemplate().getParameters().getStructure() != null) {

                TemplateDocument.Structure originalStructure = document.getPromptTemplate().getParameters().getStructure();
                Template.Structure.StructureBuilder structureBuilder = Template.Structure.builder();

                if (originalStructure.getTitle() != null) {
                    structureBuilder.title(originalStructure.getTitle());
                }

                if (originalStructure.getSections() != null) {
                    List<TemplateDocument.Section> sections = originalStructure.getSections();
                    List<Template.Section> mappedSections = new ArrayList<>();
                    sections.sort(Comparator.comparing(TemplateDocument.Section::getOrderNumber));

                    for (TemplateDocument.Section section : sections) {
                        SectionType sectionType = SectionType.valueOf(section.getType());
                        if (sectionType.equals(SectionType.TEXT_PARAGRAPH)) {
                            ParagraphTemplate paragraphTemplate = paragraphTemplateService.getTemplate(section.getSectionId());
                            if (paragraphTemplate != null) {
                                Template.Section contentItem = Template.Section.builder()
                                        .name(paragraphTemplate.getName())
                                        .description(paragraphTemplate.getDescription())
                                        .text(paragraphTemplate.getTextParagraph())
                                        .type(section.getType())
                                        .build();

                                mappedSections.add(contentItem);
                            }
                        } else if (sectionType.equals(SectionType.WIDGET)) {
                            Widget widget = widgetService.getWidget(section.getSectionId());
                            if (widget != null) {
                                Template.Section contentItem = Template.Section.builder()
                                        .name(widget.getName())
                                        .description(widget.getDescription())
                                        .type(section.getType())
                                        .build();

                                mappedSections.add(contentItem);
                            }
                        }
                    }

                    structureBuilder.sections(mappedSections);
                }


                Template.Parameters parameters = Template.Parameters.builder()
                        .structure(structureBuilder.build())
                        .build();

                promptTemplateBuilder.parameters(parameters);
            }

            promptTemplate = promptTemplateBuilder.build();
        }

        return Template.builder()
                .templateType(document.getId().getTemplateType().toString())
                .name(document.getId().getTemplateName())
                .description(document.getDescription())
                .promptTemplate(promptTemplate)
                .build();
    }
}