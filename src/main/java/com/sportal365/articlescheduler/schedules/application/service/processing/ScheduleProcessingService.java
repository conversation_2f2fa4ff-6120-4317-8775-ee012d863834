package com.sportal365.articlescheduler.schedules.application.service.processing;

import com.sportal365.articlescheduler.article.generation.application.service.AsyncArticleGenerationService;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.schedules.domain.model.enums.ScheduleStatus;
import com.sportal365.articlescheduler.schedules.domain.repository.ScheduleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

@Service
@Slf4j
public class ScheduleProcessingService implements InitializingBean {

    private final ScheduleRepository scheduleRepository;

    @Qualifier("taskScheduler")
    private final TaskScheduler taskScheduler;

    private final AsyncArticleGenerationService asyncArticleGenerationService;

    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    public ScheduleProcessingService(ScheduleRepository scheduleRepository,
                                     @Qualifier("taskScheduler")
                                     TaskScheduler taskScheduler,
                                     AsyncArticleGenerationService asyncArticleGenerationService) {
        this.scheduleRepository = scheduleRepository;
        this.taskScheduler = taskScheduler;
        this.asyncArticleGenerationService = asyncArticleGenerationService;
    }

    @Override
    public void afterPropertiesSet() {
    }

    @Scheduled(cron = "${schedule.processing.cron}") // Run once at midnight to schedule next day's tasks
    public void scheduleNextRuns() {
        log.info("Starting scheduleNextRuns at {}", LocalDateTime.now());  // Added log
        Instant startOfDay = Instant.now()
                .atZone(ZoneId.of("UTC"))
                .toLocalDate()
                .atStartOfDay(ZoneId.of("UTC"))
                .toInstant();

        Instant endOfDay = startOfDay.plus(Duration.ofDays(1));

        List<String> activeTimeZones = scheduleRepository.findDistinctTimeZonesForDateRange(
                startOfDay,
                endOfDay);

        log.info("Found {} active timezones to process: {}", activeTimeZones.size(), activeTimeZones);  // Added log

        scheduledTasks.values().forEach(task -> task.cancel(false));
        scheduledTasks.clear();

        for (String timeZone : activeTimeZones) {
            scheduleForTimeZone(timeZone);
        }
    }

    private void scheduleForTimeZone(String timeZone) {
        ZoneId zoneId = ZoneId.of(timeZone);
        ZonedDateTime nowUtc = ZonedDateTime.now(ZoneId.of("UTC"));

        ZonedDateTime nextRun = ZonedDateTime.now(zoneId)
                .withHour(0).withMinute(15).withSecond(0).withNano(0);

        if (nowUtc.isAfter(nextRun)) {
            nextRun = nextRun.plusMinutes(5);
            log.info("Current time {} is after target time {} for timezone {}, scheduling for in 5 minutes",
                    nowUtc, nextRun, timeZone);
        }

        Instant scheduledTime = nextRun.toInstant();

        ScheduledFuture<?> scheduledTask = taskScheduler.schedule(
                () -> processTimeZone(timeZone),
                scheduledTime
        );

        scheduledTasks.put(timeZone, scheduledTask);
        log.info("Scheduled next run for timezone {} at {}", timeZone, nextRun);
    }

    private void processTimeZone(String timeZone) {
        try {
            ZoneId zoneId = ZoneId.of(timeZone);
            ZonedDateTime nowZoned = ZonedDateTime.now(zoneId);

            Instant startOfDay = nowZoned.toLocalDate()
                    .atStartOfDay(zoneId)
                    .toInstant();

            Instant endOfDay = nowZoned.toLocalDate()
                    .plusDays(1)
                    .atStartOfDay(zoneId)
                    .toInstant();

            List<Schedule> schedules = scheduleRepository.findByTimeZoneAndStatusAndGenerationTimeBetween(
                    timeZone,
                    ScheduleStatus.SCHEDULED,
                    startOfDay,
                    endOfDay
            );

            log.info("Found {} schedules to process for timezone {}", schedules.size(), timeZone);  // Added log
            if (!schedules.isEmpty()) {
                log.info("Schedule details for timezone {}: {}", timeZone,   // Added detailed log
                        schedules.stream()
                                .map(s -> String.format("ID: %s, Project: %s, Generation Time: %s",
                                        s.getId(),
                                        s.getProjectDomain(),
                                        s.getGenerationTime()))
                                .toList());
            }

            List<Schedule> updatedSchedules = asyncArticleGenerationService.processSchedules(schedules);
            scheduleRepository.saveAll(updatedSchedules);

        } catch (Exception e) {
            log.error("Failed to process schedules for timezone {}: {}", timeZone, e.getMessage(), e);
        }
    }
}