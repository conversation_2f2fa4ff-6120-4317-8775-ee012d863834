package com.sportal365.articlescheduler.schedules.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class Template {

    @JsonProperty("template_type")
    private String templateType;

    private String name;

    private String description;

    @JsonProperty("prompt_template")
    private PromptTemplate promptTemplate;

    @Data
    @Builder
    public static class PromptTemplate {
        private String prompt;
        private Parameters parameters;
    }

    @Data
    @Builder
    public static class Parameters {
        private Structure structure;
    }

    @Data
    @Builder
    public static class Structure {
        private String title;
        private List<Section> sections;
    }

    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Section {
        private String name;
        private String description;
        private String text;
        private String type;
    }
}
