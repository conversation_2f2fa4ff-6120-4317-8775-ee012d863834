package com.sportal365.articlescheduler.schedules.domain.query;

import com.sportal365.articlescheduler.schedules.application.dto.request.ScheduleListRequest;
import com.sportal365.articlescheduler.schedules.domain.model.enums.ScheduleStatus;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

public class ScheduleQueryBuilder {

    public static Query buildQuery(ScheduleListRequest scheduleListRequest, Pageable pageable, String projectDomain) {
        List<Criteria> criterion = new ArrayList<>();

        criterion.add(Criteria.where("project_domain").is(projectDomain));
        ScheduleStatus scheduleStatus = ScheduleStatus.of(scheduleListRequest.getStatus());
        if (scheduleListRequest.getStatus() != null && scheduleStatus != null) {
            criterion.add(Criteria.where("status").is(scheduleStatus));
        } else {
            criterion.add(Criteria.where("status").in(List.of(ScheduleStatus.SCHEDULED,
                    ScheduleStatus.INPROGRESS, ScheduleStatus.RETRY, ScheduleStatus.FAILED)));
        }

        if (scheduleListRequest.getCategoryName() != null && !scheduleListRequest.getCategoryName().isEmpty()) {
            criterion.add(Criteria.where("category.id").is(scheduleListRequest.getCategoryName()));
        }

        if (scheduleListRequest.getCreatedBy() != null && !scheduleListRequest.getCreatedBy().isEmpty()) {
            criterion.add(Criteria.where("user_id").is(scheduleListRequest.getCreatedBy()));
        }

        if (scheduleListRequest.getCompetitionName() != null && !scheduleListRequest.getCompetitionName().isEmpty()) {
            criterion.add(Criteria.where("match_details.competition_id")
                    .is(scheduleListRequest.getCompetitionName()));
        }

        if (scheduleListRequest.getEventName() != null && !scheduleListRequest.getEventName().isEmpty()) {
            criterion.add(Criteria.where("match_details.match_name")
                    .regex(scheduleListRequest.getEventName(), "i"));
        }


        if (scheduleListRequest.getFrom() != null && scheduleListRequest.getTo() != null) {
            LocalDateTime fromDateTime = LocalDateTime.ofInstant(scheduleListRequest.getFrom(), ZoneId.systemDefault());
            LocalDateTime toDateTime = LocalDateTime.ofInstant(scheduleListRequest.getTo(), ZoneId.systemDefault());
            criterion.add(Criteria.where("generation_time").gte(fromDateTime).lte(toDateTime));
        }

        Query query = new Query();
        query.addCriteria(new Criteria().andOperator(criterion.toArray(new Criteria[0])));

        if (pageable != null) {
            query.with(pageable.getSort())
                    .skip((long) pageable.getPageNumber() * pageable.getPageSize())
                    .limit(pageable.getPageSize());
        }

        return query;
    }
}
