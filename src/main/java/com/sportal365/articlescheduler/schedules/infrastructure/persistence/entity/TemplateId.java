package com.sportal365.articlescheduler.schedules.infrastructure.persistence.entity;

import com.mongodb.lang.Nullable;
import com.sportal365.articlescheduler.shared.domain.model.enums.TemplateTypeEnum;
import lombok.*;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Objects;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TemplateId {
    @Field("project_domain")
    private String projectDomain;
    @Field("template_type")
    private TemplateTypeEnum templateType;
    @Field("template_name")
    private String templateName;

}