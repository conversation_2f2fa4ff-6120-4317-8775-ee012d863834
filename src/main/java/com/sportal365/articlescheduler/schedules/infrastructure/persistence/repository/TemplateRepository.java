package com.sportal365.articlescheduler.schedules.infrastructure.persistence.repository;

import com.sportal365.articlescheduler.schedules.infrastructure.persistence.entity.TemplateDocument;
import com.sportal365.articlescheduler.schedules.infrastructure.persistence.entity.TemplateId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TemplateRepository extends MongoRepository<TemplateDocument, TemplateId> {

    List<TemplateDocument> findAllById_ProjectDomain(String projectDomain);

    Optional<TemplateDocument> findById(TemplateId id);


}
