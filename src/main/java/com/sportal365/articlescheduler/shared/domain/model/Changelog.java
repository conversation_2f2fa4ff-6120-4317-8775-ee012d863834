package com.sportal365.articlescheduler.shared.domain.model;

import lombok.Builder;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;

@Document(collection = "changelog")
@Data
@Builder
public class Changelog {
    @Id
    private ObjectId id;
    @Field("schedule_id")
    private String scheduleId;
    @Field("match_name")
    private String matchName;
    @Field("status_old")
    private String statusOld;
    @Field("status_new")
    private String statusNew;
    @Field("updated_at")
    private Instant updatedAt;
}
