package com.sportal365.articlescheduler.shared.domain.model;

import com.sportal365.articlescheduler.shared.domain.model.enums.WidgetTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

@Document(collection = "widget")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Widget {

    @Id
    private String id;
    @Field("widget_type")
    private WidgetTypeEnum widgetType;
    private String name;
    private String version;
    private String description;
    private Date createdDate;
    private String status;
}
