package com.sportal365.articlescheduler.shared.domain.model.enums;

/**
 * Enum representing the type of schedule processing.
 * Determines when article generation should occur.
 */
public enum ScheduleType {
    /**
     * Article generation should be triggered immediately upon schedule creation.
     * No delay or queuing should occur.
     * This is the default behavior.
     */
    IMMEDIATELY,

    /**
     * Article generation should follow the standard scheduling logic.
     * Uses existing queue management and timing rules.
     */
    SCHEDULED;

    /**
     * Returns the default schedule type.
     *
     * @return IMMEDIATELY as the default schedule type
     */
    public static ScheduleType getDefault() {
        return IMMEDIATELY;
    }

    /**
     * Converts a string value to ScheduleType enum.
     *
     * @param type the string representation of the schedule type
     * @return the corresponding ScheduleType enum value, or null if not found
     */
    public static ScheduleType fromString(String type) {
        if (type == null) {
            return null;
        }

        try {
            return ScheduleType.valueOf(type.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
