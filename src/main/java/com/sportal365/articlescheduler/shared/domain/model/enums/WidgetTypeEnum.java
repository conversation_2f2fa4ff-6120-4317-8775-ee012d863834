package com.sportal365.articlescheduler.shared.domain.model.enums;

import lombok.Getter;

public enum WidgetTypeEnum {

    STANDINGS("standings"),
    SINGLE_EVENT("single-event"),
    TEAM_H2H("team-h2h"),
    PLAYER_H2H("player-h2h"),
    MATCHES_H2H("matches-h2h");

    @Getter
    private final String type;

    WidgetTypeEnum(String type) {
        this.type = type;
    }

    public static WidgetTypeEnum of(String name) {
        for (WidgetTypeEnum type : WidgetTypeEnum.values()) {
            if (type.type.equalsIgnoreCase(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant " + name);
    }
}
