package com.sportal365.articlescheduler.shared.domain.utils;

import com.sportal365.configurationclient.model.Project;
import com.sportal365.configurationclient.storage.InMemoryConfigurationStorage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProjectUtils {

    private final InMemoryConfigurationStorage inMemoryConfigurationStorage;

    public Project getProject(String projectDomain) {
        Project project = inMemoryConfigurationStorage.getProjectConfigurationMap().get(projectDomain);
        if (project == null) {
            throw new IllegalArgumentException("Project not found: " + projectDomain);
        }
        Project.Configuration.Services.AiArticleGenerationService aiArticleGenerationService = project.getConfiguration().getServices().getAiArticleGenerationService();
        if (aiArticleGenerationService == null ||
                !aiArticleGenerationService.getEnabled()) {
            throw new IllegalArgumentException("AI Article Generation service is not enabled for project: " + projectDomain);
        }
        return project;
    }
}
