package com.sportal365.articlescheduler.shared.domain.widget.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WidgetResponse {
    private String id;
    private String type;
    private WidgetData data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WidgetData {
        private String changeId;
        private WidgetConfig config;
        private String content;
        private Map<String, Object> preview;
        private String sport;

        @JsonProperty("widget_type")
        private String widgetType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WidgetConfig {
        private Map<String, Object> options;
        private String widgetId;
    }
}