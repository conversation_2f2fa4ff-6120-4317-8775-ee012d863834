package com.sportal365.articlescheduler.shared.infrastructure.client.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
public abstract class BaseApiClient {

    protected final WebClient webClient;

    protected BaseApiClient(WebClient webClient) {
        this.webClient = webClient;
    }

    protected void validateRequiredParameter(String parameterName, String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(parameterName + " parameter is required");
        }
    }

    protected void validateRequiredParameter(String parameterName, Object value) {
        if (value == null) {
            throw new IllegalArgumentException(parameterName + " parameter is required");
        }
    }

    protected void configureHeaders(org.springframework.http.HttpHeaders headers, String projectHeader) {
        headers.setAccept(java.util.List.of(ApiConstants.APPLICATION_JSON));
        headers.set(ApiConstants.HEADER_X_PROJECT, projectHeader);
    }

    protected <T> Mono<T> handleApiCall(Mono<T> apiCall, String operationName) {
        return apiCall
                .doOnSubscribe(subscription -> log.debug("Starting {} API call", operationName))
                .doOnSuccess(result -> log.debug("Successfully completed {} API call", operationName))
                .doOnError(error -> log.error("Error during {} API call: {}", operationName, error.getMessage(), error));
    }
}