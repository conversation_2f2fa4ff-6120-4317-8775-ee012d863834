package com.sportal365.articlescheduler.shared.infrastructure.config;

import com.sportal365.common.util.CacheTableWrapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

@Configuration
public class CacheTableWrapperConfig {

    @Bean
    public CacheTableWrapper cacheTableWrapper() {
        return new CacheTableWrapper(new HashMap<>(), new HashMap<>());
    }
}
