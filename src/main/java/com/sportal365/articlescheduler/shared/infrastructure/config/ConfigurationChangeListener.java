package com.sportal365.articlescheduler.shared.infrastructure.config;

import com.sportal365.articlescheduler.shared.application.service.ProjectService;
import com.sportal365.configurationclient.model.IConfiguration;
import com.sportal365.configurationclient.storage.IConfigurationChangeListener;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class ConfigurationChangeListener implements IConfigurationChangeListener {

    private final ProjectService projectService;

    @Override
    public void onConfigurationChanged(IConfiguration configuration) {
        if (projectService.isAiEnabled(configuration.getProjectName())) {
            projectService.createDefaultTemplateProject(configuration.getProjectName());
        }
    }
}
