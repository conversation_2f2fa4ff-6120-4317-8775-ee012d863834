package com.sportal365.articlescheduler.shared.infrastructure.config;

import com.sportal365.articlescheduler.shared.application.service.ProjectService;
import com.sportal365.configurationclient.storage.IConfigurationChangeListener;
import com.sportal365.configurationclient.storage.ProjectConfigurationStorage;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class ProjectChangeListenerConfiguration {

    private final ProjectConfigurationStorage projectConfigurationStorage;
    private final ProjectService projectService;

    @Bean
    IConfigurationChangeListener changeListener() {
        IConfigurationChangeListener subscriber = new ConfigurationChangeListener(projectService);

        projectConfigurationStorage.addConfigurationChangeListener(subscriber);
        return subscriber;
    }
}
