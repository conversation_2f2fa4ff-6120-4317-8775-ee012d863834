package com.sportal365.articlescheduler.shared.infrastructure.persistence.migration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * Executes the Schedule ID migration automatically on application startup
 * when the appropriate configuration flag is set.
 */
@Component
@Slf4j
@AllArgsConstructor
public class ScheduleIdMigrationRunner implements ApplicationListener<ApplicationReadyEvent> {

    private final ScheduleIdMigrationWithRollbackService migrationService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {

        log.info("Starting Schedule ID migration on application startup...");
        try {
            ScheduleIdMigrationWithRollbackService.MigrationResult result = migrationService.migrate();
            log.info("Schedule ID migration completed successfully: {}", result);
        } catch (Exception e) {
            log.error("Error during Schedule ID migration on startup", e);
        }
    }
}