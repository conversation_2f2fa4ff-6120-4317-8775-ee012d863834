package com.sportal365.articlescheduler.shared.infrastructure.persistence.migration;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.sportal365.articlescheduler.shared.domain.model.enums.TemplateTypeEnum;
import com.sportal365.articlescheduler.shared.domain.utils.ProjectUtils;
import com.sportal365.configurationclient.model.Project;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Enhanced migration service for Schedule IDs from UUID to compound ID
 * with rollback functionality to restore the original documents in case of failure
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ScheduleIdMigrationWithRollbackService {

    public static final String SCHEDULES_COLLECTION_NAME = "schedules";
    public static final String BACKUP_COLLECTION_NAME = "schedules_migration_backup";
    public static final String FIELD_ID = "_id";
    public static final String MATCH_DETAILS_FIELD = "match_details";
    public static final String MATCH_ID_FIELD = "match_id";
    public static final String PROJECT_DOMAIN_FIELD = "project_domain";
    public static final String TEMPLATE_TYPE_FIELD = "template_type";
    public static final String MIGRATION_TIMESTAMP = "migration_timestamp";
    public static final String ORIGINAL_ID_FIELD = "original_id";
    public static final String LANGUAGE_FIELD = "language";

    private final MongoTemplate mongoTemplate;
    private final ProjectUtils projectUtils;

    @Value("${migration.schedule-ids.enabled}")
    private boolean migrationEnabled;

    /**
     * Performs the migration with backup for rollback capability
     *
     * @return Migration statistics
     */
    public MigrationResult migrate() {
        MigrationResult result = new MigrationResult();
        if (!migrationEnabled) {
            log.info("Migration is disabled.");
            return result;
        }

        MongoDatabase database = mongoTemplate.getDb();
        MongoCollection<Document> schedules = database.getCollection(SCHEDULES_COLLECTION_NAME);
        createBackup(database);

        List<Document> documents = schedules.find().into(new ArrayList<>());
        result.setTotalDocuments(documents.size());

        for (Document doc : documents) {
            try {
                Object oldId = doc.get(FIELD_ID);

                if (oldId instanceof String) {
                    log.info("Skipping document with string ID: {}", oldId);
                    continue;
                }
                String projectDomain = ((Document) oldId).get("project_domain").toString();

                String language = projectUtils.getProject(projectDomain)
                        .getConfiguration()
                        .getLanguages()
                        .getDefaultLanguage()
                        .getLanguageCode();

                Document migratedDoc = new Document(doc);
                String newUuid = UUID.randomUUID().toString();
                migratedDoc.put(FIELD_ID, newUuid);
                migratedDoc.put(LANGUAGE_FIELD, language);
                migratedDoc.put(PROJECT_DOMAIN_FIELD, projectDomain);
                migratedDoc.put(ORIGINAL_ID_FIELD, oldId);
                migratedDoc.put(MIGRATION_TIMESTAMP, LocalDateTime.now().toString());

                schedules.deleteOne(Filters.eq(FIELD_ID, oldId));
                schedules.insertOne(migratedDoc);

                result.incrementMigrated();
            } catch (Exception e) {
                log.error("Migration failed for document: {}", doc.toJson(), e);
                result.incrementErrors();
            }
        }

        return result;
    }

    /**
     * Creates or reuses a single backup of the schedules that will be transformed
     * Only backs up documents with ObjectId or string IDs that will be migrated
     */
    private void createBackup(MongoDatabase database) {
        MongoCollection<Document> schedules = database.getCollection(SCHEDULES_COLLECTION_NAME);
        MongoCollection<Document> backup = database.getCollection(BACKUP_COLLECTION_NAME);

        if (backup.countDocuments() == 0) {
            List<Document> allDocuments = schedules.find().into(new ArrayList<>());
            if (!allDocuments.isEmpty()) {
                backup.insertMany(allDocuments);
                log.info("Backup completed: {} documents", allDocuments.size());
            } else {
                log.info("No documents found for backup.");
            }
        } else {
            log.info("Backup already exists. Skipping.");
        }
    }

    /**
     * Initiates a manual rollback of the migration
     *
     * @return Number of documents restored
     */
    public int rollback() {
        MongoDatabase database = mongoTemplate.getDb();
        MongoCollection<Document> schedules = database.getCollection(SCHEDULES_COLLECTION_NAME);
        MongoCollection<Document> backup = database.getCollection(BACKUP_COLLECTION_NAME);

        List<Document> backupDocs = backup.find().into(new ArrayList<>());
        int restoredCount = 0;

        for (Document doc : backupDocs) {
            Object currentId = doc.get(FIELD_ID);
            schedules.deleteOne(Filters.eq(FIELD_ID, currentId));
            schedules.insertOne(doc);
            restoredCount++;
        }

        log.info("Rollback complete: {} documents restored.", restoredCount);
        return restoredCount;
    }

    /**
     * Helper class to track migration statistics
     */
    @Getter
    @Setter
    public static class MigrationResult {
        private int totalDocuments;
        private int migrated;
        private int skipped;
        private int errors;
        private boolean rolledBack;

        public void incrementMigrated() {
            migrated++;
        }

        public void incrementSkipped() {
            skipped++;
        }

        public void incrementErrors() {
            errors++;
        }

        @Override
        public String toString() {
            return String.format("Total: %d, Migrated: %d, Skipped: %d, Errors: %d, Rolled back: %b",
                    totalDocuments, migrated, skipped, errors, rolledBack);
        }
    }
}