package com.sportal365.articlescheduler.shared.infrastructure.persistence.repository;

import com.sportal365.articlescheduler.shared.domain.model.Widget;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WidgetRepository extends MongoRepository<Widget, String> {

    Optional<Widget> findByName(String name);
    List<Widget> findByStatus(String status);
    boolean existsByName(String name);
}
