package com.sportal365.articlescheduler.sportsdata.match;

import com.sportal365.articlescheduler.shared.domain.exception.SportsDataGenerationException;
import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class MatchDetailsService {

	private final MatchDetailPipelineService pipelineService;

	public MatchDetails getMatchDetails(Schedule schedule) {
		log.debug("Background article generation started for Schedule ID: {}", schedule.getId());

		try {
			List<MatchDetailEnrichService> pipeline = pipelineService.getPipeline(schedule);
			MatchDetails matchDetails = MatchDetails.builder().build();
			for (MatchDetailEnrichService enrichService : pipeline) {
				matchDetails = enrichService.enrich(matchDetails, schedule);
			}
			matchDetails.setMatchId(schedule.getMatchDetails().getMatchId());
			log.debug("Background article generation completed for Schedule ID: {}", schedule.getId());
			return matchDetails;

		} catch (Exception e) {
			throw new SportsDataGenerationException(
					"Failed to generate sports data for schedule ID: " + schedule.getId() +
							" with error: " + e.getMessage()
			);
		}
	}
}
