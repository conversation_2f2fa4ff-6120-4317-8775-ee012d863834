package com.sportal365.articlescheduler.sportsdata.sportal365.football.mappers;

import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.enums.LineupStatus;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.MatchLineupResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@Slf4j
public class LineupMapper {

    public MatchDetails.Lineup map(MatchLineupResponse response) {

        String statusString = response.getStatus();
        LineupStatus lineupStatus = LineupStatus.fromString(statusString);

        if (lineupStatus.equals(LineupStatus.NOT_AVAILABLE)) {

            return null;
        }
        MatchDetails.Lineup.TeamDetails homeTeamDetails = getTeamDetails(response.getHomeTeam());
        MatchDetails.Lineup.TeamDetails awayTeamDetails = getTeamDetails(response.getAwayTeam());

        return MatchDetails.Lineup.builder()
                .status(response.getStatus())
                .homeTeam(homeTeamDetails)
                .awayTeam(awayTeamDetails)
                .build();
    }

    private MatchDetails.Lineup.TeamDetails getTeamDetails(MatchLineupResponse.Team team) {

        return MatchDetails.Lineup.TeamDetails.builder()
                .formation(team.getFormation())
                .coach(getCoachDetails(team.getCoach()))
                .players(team.getPlayers().stream()
                        .map(this::getPlayerDetails)
                        .collect(Collectors.toList()))
                .build();

    }

    private MatchDetails.Lineup.TeamDetails.Coach getCoachDetails(MatchLineupResponse.Team.Coach coach) {

        return MatchDetails.Lineup.TeamDetails.Coach.builder()
                .name(coach.getName())
                .build();
    }

    private MatchDetails.Lineup.TeamDetails.Player getPlayerDetails(MatchLineupResponse.Team.Player player) {

        return MatchDetails.Lineup.TeamDetails.Player.builder()
                .type(getPlayerType(player.getType()))
                .player(getPlayerDetails(player.getPlayer()))
                .shirtNumber(player.getShirtNumber())
                .build();
    }

    private MatchDetails.Lineup.TeamDetails.Player.Type getPlayerType(MatchLineupResponse.Team.Player.Type player) {

        return  MatchDetails.Lineup.TeamDetails.Player.Type.builder()
                .name(player.getName())
                .category(player.getCategory())
                .build();
    }

    private MatchDetails.Lineup.TeamDetails.Player.PlayerDetails getPlayerDetails(MatchLineupResponse.Team.Player.PlayerDetails player) {

        return MatchDetails.Lineup.TeamDetails.Player.PlayerDetails.builder()
                .name(player.getName())
                .position(player.getPosition())
                .birthdate(player.getBirthdate())
                .build();
    }
}
