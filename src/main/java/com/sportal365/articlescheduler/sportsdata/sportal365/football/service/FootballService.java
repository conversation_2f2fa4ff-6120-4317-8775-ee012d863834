package com.sportal365.articlescheduler.sportsdata.sportal365.football.service;

import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.match.MatchDetailEnrichService;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.FootballApiClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.LastMeetingsResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.MatchDetailsResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.MatchLineupResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.client.model.TeamSquadResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.mappers.LastMeetingMapper;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.mappers.LineupMapper;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.mappers.MatchDetailsMapper;
import com.sportal365.articlescheduler.sportsdata.sportal365.football.mappers.TeamSquadMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class FootballService implements MatchDetailEnrichService {

    private final FootballApiClient footballApiClient;

    private final LineupMapper lineupMapper;

    private final TeamSquadMapper teamSquadMapper;

    private final LastMeetingMapper lastMeetingMapper;

    private final MatchDetailsMapper matchDetailsMapper;

    public MatchLineupResponse getLineups(String matchUuid, String project) {
        return footballApiClient.getLineups(matchUuid, project).block();
    }

    public List<TeamSquadResponse> getTeamSquads(List<String> teamIds, String project) {
        List<TeamSquadResponse> teamSquadResponses = new ArrayList<>();
        for (String teamId : teamIds) {
            teamSquadResponses.add(footballApiClient.getTeamSquad(teamId, project).block());
        }

        return teamSquadResponses;
    }

    /**
     * Get match details including referee and venue information
     *
     * @param matchUuid The UUID of the match
     * @return Match details response
     */
    public MatchDetailsResponse getEventDetails(String matchUuid, String languageCode, String project) {
        return footballApiClient.getMatchDetails(matchUuid, languageCode, project).block();
    }

    /**
     * Get a previous match between two teams
     *
     * @param teamIds List of team IDs to find matches for
     * @return List of matches between the teams
     */
    public LastMeetingsResponse getLastMeetingsBetweenTeams(List<String> teamIds, String languageCode, String project) {
        try {
            LastMeetingsResponse lastMatchResponse = footballApiClient.getMatchesBetweenTeams(teamIds, "desc", "FINISHED", 5, languageCode, project).block();
            return Objects.requireNonNullElseGet(lastMatchResponse, LastMeetingsResponse::new);
        } catch (Exception e) {
            log.error("Error fetching last meetings for teams {}: {}", teamIds, e.getMessage());
            return new LastMeetingsResponse(); // Return empty response on error
        }
    }

    @Override
    public MatchDetails enrich(MatchDetails matchDetails, Schedule schedule) {

        String projectDomain = schedule.getProjectDomain();
        MatchDetailsResponse matchDetailsResponse = getEventDetails(schedule.getMatchDetails().getMatchId(), schedule.getLanguage(),
                projectDomain);

        List<String> teams = List.of(matchDetails.getHomeTeamLegacyId(), matchDetails.getAwayTeamLegacyId());

        LastMeetingsResponse lastMeetings = getLastMeetingsBetweenTeams(teams, schedule.getLanguage(), projectDomain);
        return matchDetails.toBuilder()
                .lineup(lineupMapper.map(getLineups(schedule.getMatchDetails().getMatchId(), projectDomain)))
                .squad(teamSquadMapper.map(getTeamSquads(teams, projectDomain)))
                .lastMeeting(lastMeetingMapper.map(lastMeetings))
                .lastMeetings(lastMeetingMapper.mapToList(lastMeetings))
                .referee(matchDetailsMapper.getReferee(matchDetailsResponse))
                .venue(matchDetailsMapper.getVenue(matchDetailsResponse))
                .build();
    }
}
