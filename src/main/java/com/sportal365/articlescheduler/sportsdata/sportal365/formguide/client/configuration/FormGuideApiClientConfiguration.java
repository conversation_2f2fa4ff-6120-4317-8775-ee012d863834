package com.sportal365.articlescheduler.sportsdata.sportal365.formguide.client.configuration;

import com.sportal365.articlescheduler.shared.infrastructure.client.common.AbstractApiClientConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class FormGuideApiClientConfiguration extends AbstractApiClientConfiguration {

    @Value("${form.guide.api.base.url}")
    private String formGuideApiBaseUrl;

    @Value("${football.api.username}")
    private String username;

    @Value("${football.api.password}")
    private String password;

    @Bean
    public WebClient formGuideApiWebClient() {
        return createWebClient(formGuideApiBaseUrl, username, password);
    }
}
