package com.sportal365.articlescheduler.sportsdata.sportal365.standings.mappers;

import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model.StandingGroup;
import com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model.StandingResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model.StandingsDataResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model.StandingsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class StandingsMapper {

    public MatchDetails.Standings map(StandingsResponse standingsResponse,
                                      List<String> participantIds) {

        if (participantIds == null || participantIds.size() < 2) {
            log.warn("No (or only one) participant ID provided. Cannot build Standings.");
            return null;
        }

        if (standingsResponse.getData() == null || standingsResponse.getData().isEmpty()) {
            log.warn("No data found in StandingsResponse.");
            return null;
        }

        StandingsDataResponse dataResponse = standingsResponse.getData().get(0);

        if (dataResponse.getStandings() == null || dataResponse.getStandings().isEmpty()) {
            log.warn("No 'standings' found in dataResponse.");
            return null;
        }

        StandingGroup group = dataResponse.getStandings().get(0); // e.g. the "Overall" group
        List<StandingResponse> standingList = group.getStandingResponse();
        if (standingList == null) {
            log.warn("No 'standing' array found in the group.");
            return null;
        }

        MatchDetails.Standings.TeamStandings homeTeam = buildTeamStandings(standingList, participantIds.get(0));
        MatchDetails.Standings.TeamStandings awayTeam = buildTeamStandings(standingList, participantIds.get(1));

        // Build final Standings
        return MatchDetails.Standings.builder()
                .isPlayoffs(standingsResponse.isPlayoffs())
                .homeTeam(homeTeam)
                .awayTeam(awayTeam)
                .build();
    }

    private MatchDetails.Standings.TeamStandings buildTeamStandings(List<StandingResponse> standingList, String participantId) {
        // 1) Find the matching StandingResponse for this participant
        Optional<StandingResponse> matchOpt = standingList.stream()
                .filter(s -> s.getTeamResponse() != null
                        && participantId.equals(s.getTeamResponse().getId()))
                .findFirst();

        if (matchOpt.isEmpty()) {
            // If no match is found, return empty fallback
            return MatchDetails.Standings.TeamStandings.builder().build();
        }

        StandingResponse standing = matchOpt.get();

        // 2) Extract the columns we need
        int rank = getColumnValueAsInt(standing, "RANK");
        int points = getColumnValueAsInt(standing, "POINTS");
        int played = getColumnValueAsInt(standing, "PLAYED");
        int goalsFor = getColumnValueAsInt(standing, "GOALS_FOR");
        int goalsAgainst = getColumnValueAsInt(standing, "GOALS_AGAINST");

        String form = "";

        return MatchDetails.Standings.TeamStandings.builder()
                .position(rank)
                .points(points)
                .gamesPlayed(played)
                .goalsScored(goalsFor)
                .goalsConceded(goalsAgainst)
                .form(form)
                .build();
    }

    private int getColumnValueAsInt(StandingResponse standing, String code) {
        if (standing.getColumnResponses() == null) {
            return 0;
        }
        // find the column with type.code == code
        return standing.getColumnResponses().stream()
                .filter(col -> col.getType() != null && code.equals(col.getType().getCode()))
                .findFirst()
                .map(col -> safeParse(col.getValue()))
                .orElse(0);
    }

    private int safeParse(String value) {
        try {
            return Integer.parseInt(value);
        } catch (Exception e) {
            return 0;
        }
    }
}
