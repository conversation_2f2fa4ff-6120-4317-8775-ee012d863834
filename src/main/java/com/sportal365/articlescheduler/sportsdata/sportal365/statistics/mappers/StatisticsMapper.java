package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.mappers;

import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.EventDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.ParticipantDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.wrappers.StarResponsesWrapper;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class StatisticsMapper {

    public MatchDetails.ParticipantStatistics map(List<StarResponsesWrapper> statisticsPerParticipant) {
        // Defensive check: you expect exactly 2 items: home star player(index 0) and away star player (index 1)
        if (statisticsPerParticipant == null || statisticsPerParticipant.size() != 2) {
            MatchDetails.ParticipantStatistics.Team emptyTeam = MatchDetails.ParticipantStatistics.Team.builder().build();

            // Return empty if data is insufficient
            return MatchDetails.ParticipantStatistics.builder()
                    .homeTeam(emptyTeam)
                    .awayTeam(emptyTeam)
                    .build();
        }

        return MatchDetails.ParticipantStatistics.builder()
                .homeTeam(toTeamStatistics(statisticsPerParticipant.get(0)))
                .awayTeam(toTeamStatistics(statisticsPerParticipant.get(1)))
                .build();
    }

    private MatchDetails.ParticipantStatistics.Team toTeamStatistics(StarResponsesWrapper participantStatistics) {
        String participantId = participantStatistics.getParticipantWithAggregateStatistics().getId();
        String participantName = participantStatistics.getParticipantWithAggregateStatistics().getName();

        // Extract player's season statistics
        List<MatchDetails.ParticipantStatistics.Statistic> seasonAggregateStats =
                extractAggregateStatistics(participantStatistics.getParticipantWithAggregateStatistics().getStatistics());

        // Extract latest event statistics
        List<MatchDetails.ParticipantStatistics.EventStatistics> latestEventStats =
                extractEventStatistics(participantStatistics.getParticipantLatestEventsResponse().getData(), participantName);

        // Extract latest versus event statistics
        List<MatchDetails.ParticipantStatistics.EventStatistics> latestVersusStats =
                extractEventStatistics(participantStatistics.getParticipantLatestVersusEventResponse().getData(), participantName);

        // Build and return the team object
        return buildTeam(
                participantStatistics.getTeamName(),
                participantId,
                participantName,
                seasonAggregateStats,
                latestEventStats,
                latestVersusStats
        );
    }

    // Convert individual statistics
    private List<MatchDetails.ParticipantStatistics.Statistic> extractAggregateStatistics(List<StatisticDto> statisticDtos) {
        return statisticDtos.stream()
                .map(this::convertToStatistic)
                .toList();
    }

    private MatchDetails.ParticipantStatistics.Statistic convertToStatistic(StatisticDto statisticDto) {
        return MatchDetails.ParticipantStatistics.Statistic.builder()
                .name(statisticDto.getName())
                .value(statisticDto.getValue())
                .build();
    }

    // Extract event statistics for a participant
    private List<MatchDetails.ParticipantStatistics.EventStatistics> extractEventStatistics(
            List<EventDto> events, String participantName) {

        return events.stream()
                .map(eventDto -> findParticipantInEvent(eventDto, participantName))
                .filter(Objects::nonNull)
                .map(this::convertToEventStatistics)
                .toList();
    }

    // Find participant in an event
    private Pair<String, ParticipantDto> findParticipantInEvent(EventDto eventDto, String participantName) {
        ParticipantDto participant = eventDto.getParticipants().stream()
                .filter(p -> p.getName().equals(participantName))
                .findFirst()
                .orElse(null);

        return participant != null ? Pair.of(eventDto.getName(), participant) : null;
    }

    // Convert to event statistics
    private MatchDetails.ParticipantStatistics.EventStatistics convertToEventStatistics(Pair<String, ParticipantDto> pair) {
        return MatchDetails.ParticipantStatistics.EventStatistics.builder()
                .eventName(pair.getFirst())
                .statistics(extractAggregateStatistics(pair.getSecond().getStatistics()))
                .build();
    }

    // Build the final team object
    private MatchDetails.ParticipantStatistics.Team buildTeam(
            String teamName,
            String participantId,
            String participantName,
            List<MatchDetails.ParticipantStatistics.Statistic> seasonStats,
            List<MatchDetails.ParticipantStatistics.EventStatistics> latestEventStats,
            List<MatchDetails.ParticipantStatistics.EventStatistics> latestVersusStats) {

        return MatchDetails.ParticipantStatistics.Team.builder()
                .name(teamName)
                .starPlayer(MatchDetails.ParticipantStatistics.Player.builder()
                        .id(participantId)
                        .name(participantName)
                        .averageSeasonStatistics(seasonStats)
                        .latestEventsStatistics(latestEventStats)
                        .latestEventsStatisticsVersusOpponent(latestVersusStats)
                        .build())
                .build();
    }

}
