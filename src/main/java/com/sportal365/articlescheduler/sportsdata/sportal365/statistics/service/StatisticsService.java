package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.service;

import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.match.MatchDetailEnrichService;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.StatisticsApiClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.ParticipantDto;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiAggregateResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.StatisticsApiEventsResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.wrappers.StarResponsesWrapper;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.client.model.wrappers.TeamInfoWrapper;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.mappers.StatisticsMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util.StatisticsExtractionHelper.*;

@Service
@RequiredArgsConstructor
public class StatisticsService implements MatchDetailEnrichService {

    private final StatisticsApiClient statisticsApiClient;

    private final StatisticsMapper statisticsMapper;

    public MatchDetails enrich(MatchDetails matchDetails, Schedule schedule) {
        List<StarResponsesWrapper> starPlayerInfoWrappers = collectPlayerStatistics(
                schedule.getProjectDomain(),
                matchDetails
        );

        return matchDetails.toBuilder()
                .statistics(statisticsMapper.map(starPlayerInfoWrappers))
                .build();
    }

    private List<StarResponsesWrapper> collectPlayerStatistics(String project, MatchDetails matchDetails) {
        if (matchDetails.getParticipantIds() == null) {
            return Collections.emptyList();
        }

        List<String> lastMeetingIds = extractLastMeetingIds(matchDetails);
        List<StarResponsesWrapper> starResponsesWrappers = new ArrayList<>();

        for (String participantId : matchDetails.getParticipantIds()) {
            // Determine team details based on participant ID
            TeamInfoWrapper teamInfoWrapper = determineTeamInfo(participantId, matchDetails);

            // Get season statistics for the team
            StatisticsApiAggregateResponse teamSeasonStats =
                    statisticsApiClient.getParticipantSeasonStatistics(
                            project, participantId, matchDetails.getSeasonId()).block();

            // Return no statistics if we can't retrieve a Statistics API response
            if (teamSeasonStats == null) {
                return Collections.emptyList();
            }

            // Find the highest rated player in the team
            ParticipantDto highestRatedPlayer = findHighestRatedPlayer(teamSeasonStats, teamInfoWrapper.players());

            // Return no statistics if we can't determine either or both teams' star players
            if (highestRatedPlayer == null) {
                return Collections.emptyList();
            }

            // Get detailed statistics for the star player
            StarResponsesWrapper playerStats = collectStarPlayerStatistics(
                    project,
                    highestRatedPlayer,
                    matchDetails.getSeasonId(),
                    lastMeetingIds,
                    teamInfoWrapper.name());

            starResponsesWrappers.add(playerStats);
        }

        return starResponsesWrappers;
    }

    private StarResponsesWrapper collectStarPlayerStatistics(
            String project,
            ParticipantDto player,
            String seasonId,
            List<String> lastMeetingIds,
            String teamName) {

        // Get player's performance in latest events
        StatisticsApiEventsResponse playerLatestEvents =
                statisticsApiClient.getParticipantSeasonStatisticsDiscrete(
                        project, player.getId(), seasonId).block();

        // Get player's performance against the opponent
        StatisticsApiEventsResponse playerLatestVersusEvents;
        if (!lastMeetingIds.isEmpty()) {
            playerLatestVersusEvents = statisticsApiClient.getParticipantStatisticsDiscrete(
                    project, player.getId(), lastMeetingIds).block();
        } else {
            playerLatestVersusEvents = new StatisticsApiEventsResponse();
        }

        return new StarResponsesWrapper(
                teamName,
                player,
                playerLatestEvents,
                playerLatestVersusEvents);
    }
}
