package com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util;


import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.sportsdata.sportal365.statistics.constants.StatisticsConstants;

/**
 * Factory for the abstraction class
 * {@link com.sportal365.articlescheduler.sportsdata.sportal365.statistics.util.StatisticsPlayer}
 */
public class StatisticsPlayerFactory {

    public static StatisticsPlayer createFromLineupPlayer(MatchDetails.Lineup.TeamDetails.Player player) {
        return StatisticsPlayer.builder()
                .name(player.getPlayer().getName())
                .lineupStatus(player.getType().getName())
                .active(true) // If they are in the lineup, they are bound to be active
                .position(player.getPlayer().getPosition())
                .build();
    }

    public static StatisticsPlayer createFromSquadPlayer(MatchDetails.Squad.Player player) {
        return StatisticsPlayer.builder()
                .name(player.getName())
                .lineupStatus(StatisticsConstants.LINEUP_TYPE_UNKNOWN)
                .active(player.isActive())
                .position(player.getPosition())
                .build();
    }

}
