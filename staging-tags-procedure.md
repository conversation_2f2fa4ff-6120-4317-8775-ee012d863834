# Staging Tags Procedure

## Overview

This document outlines the process for creating and managing staging tags for our services, which is a crucial step in our development and release workflow.

## Process Flow

1. Pull Request (PR) Creation
2. Code Review
3. Integration and Functional Review
4. Staging Tag Creation
5. Quality Assurance (QA)

## Detailed Steps

### 1. Pull Request (PR) Creation

- Developer creates a ticket and submits a Pull Request

### 2. Code Review

- A code reviewer examines the PR
- If approved, the reviewer merges the code into the integration branch

### 3. Integration and Functional Review

- Code reviewer performs a functional review on the integration branch
- If functional review passes, reviewer asks the developer to proceed to staging

### 4. Staging Tag Creation

1. Check the latest release version in the Bitbucket repository of the API

2. Create a new tag using the format `prerelease-X.Y.Z`, where X.Y.Z is the next appropriate version number.
   
   Example:
   ```bash
   git tag prerelease-1.1.0
   ```

3. If the tag exists locally (old tag):
   
   a. Delete the local tag
   ```bash
   git tag -d prerelease-1.1.0
   ```
   
   b. Fetch the tags
   ```bash
   git fetch --tags
   ```
   
   c. Checkout the desired tag
   ```bash
   git checkout prerelease-1.1.0
   ```
   
   d. <PERSON> pick the wanted commit
   ```bash
   git cherry-pick {commit-hash}
   ```
   
   After this point it continues the same as the 4th point

4. If the tag already exists:
   
   1. Fetch the needed commits locally (This depends if you need cherry-pick or you want the latest master)
   
   2. Force create the tag locally
      ```bash
      git tag -f prerelease-1.0.0
      ```
   
   3. Push the new tag to the remote repository
      ```bash
      git push --force origin prerelease-1.0.0
      ```

### 5. Quality Assurance (QA)

- Developer moves the ticket to the QA stage for testing

## Best Practices

- Always check the latest release version before creating a new tag
- Communicate with team members when updating existing tags
- Ensure all necessary commits are included before creating or updating a tag

## Troubleshooting

- If you encounter issues with tag creation or deletion, consult with your team lead or DevOps support
